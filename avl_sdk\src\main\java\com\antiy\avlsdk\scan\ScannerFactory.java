package com.antiy.avlsdk.scan;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.IScanner;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.callback.ScanStatisticsCallback;
import com.antiy.avlsdk.entity.ScanMode;

import java.io.File;
import java.util.List;

public class ScannerFactory {
    public static IScanner createScanner(ScanMode mode, List<File> files, ScanListener callback) {
        BaseScanner scanner;

        switch (mode) {
            case CLOUD:
                scanner = new CloudScanner(files, callback, true);
                break;
            case LOCAL:
                scanner = new LocalScanner(files, callback);
                break;
            default:
                throw new IllegalArgumentException("Unsupported scan mode: " + mode);
        }

        // 设置统计信息
        AVLEngine engine = AVLEngine.getInstance();
        if (engine.isStatisticsEnabled()) {
            scanner.setStatisticsInfo(engine.getStatisticsCallback(), engine.getCurrentSessionId());
        }

        return scanner;
    }
}
