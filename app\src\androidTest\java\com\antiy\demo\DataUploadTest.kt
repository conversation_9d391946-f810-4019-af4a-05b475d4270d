package com.antiy.demo

import android.content.Context
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.callback.RequestCallback
import com.antiy.avlsdk.entity.RequestMethod
import com.antiy.avlsdk.upload.DataUploadManager
import com.antiy.avlsdk.upload.DeviceInfoManager
import org.json.JSONObject
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * 数据回传功能测试类
 * 
 * Author: AVL SDK Team
 * Date: 2025-01-04
 */
@RunWith(AndroidJUnit4::class)
class DataUploadTest {
    
    private lateinit var context: Context
    
    @Before
    fun setUp() {
        context = InstrumentationRegistry.getInstrumentation().targetContext
    }
    
    @Test
    fun testDeviceInfoManager() {
        // 测试获取设备 ID
        val deviceId = DeviceInfoManager.getDeviceId(context)
        println("Device ID: $deviceId")
        
        // 测试获取车架号 ID
        val testVehicleId = "TEST_VEHICLE_123456"
        val vehicleId = DeviceInfoManager.getVehicleId(testVehicleId)
        assert(vehicleId == testVehicleId)
        
        // 测试空车架号 ID 异常
        try {
            DeviceInfoManager.getVehicleId("")
            assert(false) { "Should throw exception for empty vehicle ID" }
        } catch (e: IllegalArgumentException) {
            // Expected
        }
    }
    
    @Test
    fun testDataUploadManager() {
        val latch = CountDownLatch(1)
        var uploadSuccess = false
        var uploadData: JSONObject? = null
        
        // 创建测试用的 NetworkManager
        val testNetworkManager = object : NetworkManager() {
            override fun request(
                uri: String?,
                method: RequestMethod?,
                param: Any?,
                callback: RequestCallback?
            ) {
                // 验证上传的数据
                uploadData = param as? JSONObject
                uploadSuccess = true
                
                // 模拟成功响应
                callback?.onFinish(200, "Upload successful")
                latch.countDown()
            }
        }
        
        // 初始化 AVLEngine
        AVLEngine.init(context, "TEST_VEHICLE_123456", ALog(context), testNetworkManager)
        
        // 执行数据上传
        DataUploadManager.getInstance().uploadDeviceInfo(context, "TEST_VEHICLE_123456")
        
        // 等待上传完成
        latch.await(10, TimeUnit.SECONDS)
        
        // 验证结果
        assert(uploadSuccess) { "Upload should be successful" }
        assert(uploadData != null) { "Upload data should not be null" }
        
        uploadData?.let { data ->
            assert(data.has("brand")) { "Should contain brand" }
            assert(data.has("model")) { "Should contain model" }
            assert(data.has("language")) { "Should contain language" }
            assert(data.has("deviceId")) { "Should contain deviceId" }
            assert(data.has("vehicleId")) { "Should contain vehicleId" }
            assert(data.getString("vehicleId") == "TEST_VEHICLE_123456") { "Vehicle ID should match" }
        }
    }
    
    @Test
    fun testAVLEngineIntegration() {
        val latch = CountDownLatch(1)
        var dataUploadTriggered = false
        
        // 创建测试用的 NetworkManager
        val testNetworkManager = object : NetworkManager() {
            override fun request(
                uri: String?,
                method: RequestMethod?,
                param: Any?,
                callback: RequestCallback?
            ) {
                if (uri?.contains("/api/device/upload") == true) {
                    dataUploadTriggered = true
                    callback?.onFinish(200, "Upload successful")
                    latch.countDown()
                }
            }
        }
        
        // 初始化 AVLEngine，应该自动触发数据上传
        val result = AVLEngine.init(context, "TEST_VEHICLE_789", ALog(context), testNetworkManager)
        
        // 等待数据上传
        latch.await(10, TimeUnit.SECONDS)
        
        // 验证初始化成功且数据上传被触发
        assert(result.isSuccess) { "AVLEngine init should be successful" }
        assert(dataUploadTriggered) { "Data upload should be triggered automatically" }
    }
}
