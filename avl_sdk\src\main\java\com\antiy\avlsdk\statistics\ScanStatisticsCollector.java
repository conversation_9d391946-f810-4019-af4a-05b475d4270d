package com.antiy.avlsdk.statistics;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.statistics.entity.EngineStatistics;
import com.antiy.avlsdk.statistics.entity.PerformanceData;
import com.antiy.avlsdk.statistics.entity.ScanSessionRecord;
import com.antiy.avlsdk.statistics.entity.ScanStatistics;
import com.antiy.avlsdk.statistics.entity.ThreatInfo;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 扫描统计数据收集器
 * 负责在扫描过程中收集各种统计数据
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ScanStatisticsCollector {
    
    private static final String TAG = "ScanStatisticsCollector";
    
    /** 当前扫描会话记录 */
    private ScanSessionRecord currentSession;
    
    /** 扫描会话ID计数器 */
    private static final AtomicInteger sessionCounter = new AtomicInteger(1);
    
    /** 日期格式化器 */
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
    
    /** 性能监控开关 */
    private boolean performanceMonitorEnabled = true;
    
    /** 上次性能采样时间 */
    private long lastPerformanceSampleTime = 0;
    
    /** 性能采样间隔（毫秒） */
    private static final long PERFORMANCE_SAMPLE_INTERVAL = 1000; // 1秒
    
    public ScanStatisticsCollector() {
    }
    
    /**
     * 开始新的扫描会话
     */
    public ScanSessionRecord startScanSession(String scanType, String scanPath) {
        String sessionId = generateSessionId();
        currentSession = new ScanSessionRecord(sessionId, scanType, scanPath);
        
        AVLEngine.Logger.info(TAG + ": Started scan session: " + sessionId);
        return currentSession;
    }
    
    /**
     * 结束当前扫描会话
     */
    public ScanSessionRecord finishScanSession() {
        if (currentSession == null) {
            AVLEngine.Logger.warn(TAG + ": No active scan session to finish");
            return null;
        }
        
        currentSession.markCompleted();
        currentSession.statistics.finalize();
        currentSession.engineStats.finalize();
        currentSession.performance.finalize();

        AVLEngine.Logger.info(TAG + ": Finished scan session: " + currentSession.scanId);
        AVLEngine.Logger.info(TAG + ": Final statistics - totalFiles: " + currentSession.statistics.totalFiles +
                             ", scannedFiles: " + currentSession.statistics.scannedFiles +
                             ", maliciousFiles: " + currentSession.statistics.maliciousFiles);
        AVLEngine.Logger.info(TAG + ": Session summary: " + currentSession.getSummary());
        
        ScanSessionRecord finishedSession = currentSession;
        currentSession = null;
        
        return finishedSession;
    }
    
    /**
     * 标记扫描会话失败
     */
    public ScanSessionRecord failScanSession(String errorMessage) {
        if (currentSession == null) {
            return null;
        }
        
        currentSession.markFailed(errorMessage);
        currentSession.statistics.finalize();
        currentSession.engineStats.finalize();
        currentSession.performance.finalize();
        
        AVLEngine.Logger.error(TAG + ": Scan session failed: " + currentSession.scanId + ", error: " + errorMessage);
        
        ScanSessionRecord failedSession = currentSession;
        currentSession = null;
        
        return failedSession;
    }
    
    /**
     * 标记扫描会话取消
     */
    public ScanSessionRecord cancelScanSession() {
        if (currentSession == null) {
            return null;
        }
        
        currentSession.markCancelled();
        currentSession.statistics.finalize();
        currentSession.engineStats.finalize();
        currentSession.performance.finalize();
        
        AVLEngine.Logger.info(TAG + ": Scan session cancelled: " + currentSession.scanId);
        
        ScanSessionRecord cancelledSession = currentSession;
        currentSession = null;
        
        return cancelledSession;
    }
    
    /**
     * 记录扫描开始
     */
    public void onScanStart(int totalFiles) {
        if (currentSession == null) {
            AVLEngine.Logger.error(TAG + ": onScanStart called but currentSession is null");
            return;
        }

        currentSession.statistics.totalFiles = totalFiles;
        AVLEngine.Logger.info(TAG + ": Scan started with " + totalFiles + " files, session=" + currentSession.scanId);
        AVLEngine.Logger.info(TAG + ": Statistics totalFiles set to: " + currentSession.statistics.totalFiles);
    }

    /**
     * 设置总文件数（专门用于scanCount调用）
     */
    public void setTotalFileCount(int totalFiles) {
        if (currentSession == null) {
            AVLEngine.Logger.error(TAG + ": setTotalFileCount called but currentSession is null");
            return;
        }

        // 只有当totalFiles还是0或者新的数量更大时才更新
        if (currentSession.statistics.totalFiles == 0 || totalFiles > currentSession.statistics.totalFiles) {
            currentSession.statistics.totalFiles = totalFiles;
            AVLEngine.Logger.info(TAG + ": Updated totalFiles to: " + totalFiles + " for session: " + currentSession.scanId);
        } else {
            AVLEngine.Logger.info(TAG + ": Keeping existing totalFiles: " + currentSession.statistics.totalFiles +
                                 " (new count: " + totalFiles + ")");
        }
    }
    
    /**
     * 记录文件扫描开始
     */
    public void onFileStart(String filePath) {
        if (currentSession == null) {
            return;
        }
        
        // 可以在这里记录文件开始扫描的时间等信息
        samplePerformanceIfNeeded();
    }
    
    /**
     * 记录文件扫描完成
     */
    public void onFileFinish(String filePath, ResultScan result, String engine, long scanTime) {
        if (currentSession == null || result == null) {
            return;
        }
        
        ScanStatistics stats = currentSession.statistics;
        EngineStatistics engineStats = currentSession.engineStats;
        
        // 更新基础统计
        stats.incrementScannedFiles();
        stats.recordScanTime(scanTime);
        
        // 更新引擎统计
        recordEngineStats(engineStats, engine, result.isMalicious, scanTime);
        
        // 处理扫描结果
        if (result.isMalicious) {
            stats.incrementMaliciousFiles();

            // 创建威胁信息（不要重复增加maliciousFiles计数）
            ThreatInfo threat = ThreatInfo.fromResultScan(filePath, result, engine, scanTime);
            if (threat != null) {
                // 直接添加到威胁列表，不调用addThreat方法（避免重复计数）
                currentSession.threats.add(threat);
            }
        } else {
            stats.incrementCleanFiles();
        }
        
        // 更新云查统计
        if (result.isCloudScan) {
            stats.incrementCloudScanFiles();
        } else {
            stats.incrementLocalScanFiles();
        }
        
        // 更新性能数据
        currentSession.performance.incrementIoOperations();
        if (result.isCloudScan) {
            currentSession.performance.incrementNetworkRequests(0); // 实际字节数需要从网络层获取
        }
        
        samplePerformanceIfNeeded();
    }
    
    /**
     * 记录文件跳过
     */
    public void onFileSkipped(String filePath, String reason) {
        if (currentSession == null) {
            return;
        }
        
        currentSession.statistics.incrementSkippedFiles();
        
        if ("cache_hit".equals(reason)) {
            currentSession.statistics.incrementCacheHitFiles();
            currentSession.performance.recordCacheHit();
        } else {
            currentSession.performance.recordCacheMiss();
        }
    }
    
    /**
     * 记录文件扫描错误
     */
    public void onFileError(String filePath, String errorMessage) {
        if (currentSession == null) {
            return;
        }
        
        currentSession.statistics.incrementErrorFiles();
        AVLEngine.Logger.warn(TAG + ": File scan error: " + filePath + ", error: " + errorMessage);
    }
    
    /**
     * 获取当前扫描会话
     */
    public ScanSessionRecord getCurrentSession() {
        return currentSession;
    }
    
    /**
     * 检查是否有活跃的扫描会话
     */
    public boolean hasActiveSession() {
        return currentSession != null;
    }
    
    /**
     * 设置性能监控开关
     */
    public void setPerformanceMonitorEnabled(boolean enabled) {
        this.performanceMonitorEnabled = enabled;
    }
    
    /**
     * 生成扫描会话ID
     */
    private String generateSessionId() {
        String timestamp = DATE_FORMAT.format(new Date());
        int counter = sessionCounter.getAndIncrement();
        return String.format("scan_%s_%03d", timestamp, counter);
    }
    
    /**
     * 记录引擎统计
     */
    private void recordEngineStats(EngineStatistics engineStats, String engine, boolean isMalicious, long scanTime) {
        switch (engine) {
            case "mobile":
                engineStats.recordMobileEngineScan(isMalicious, scanTime);
                break;
            case "pc":
                engineStats.recordPcEngineScan(isMalicious, scanTime);
                break;
            case "cloud":
                engineStats.recordCloudEngineScan(isMalicious, scanTime);
                break;
            default:
                AVLEngine.Logger.warn(TAG + ": Unknown engine type: " + engine);
                break;
        }
    }
    
    /**
     * 按需采样性能数据
     */
    private void samplePerformanceIfNeeded() {
        if (!performanceMonitorEnabled || currentSession == null) {
            return;
        }
        
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastPerformanceSampleTime >= PERFORMANCE_SAMPLE_INTERVAL) {
            samplePerformanceData();
            lastPerformanceSampleTime = currentTime;
        }
    }
    
    /**
     * 采样性能数据
     */
    private void samplePerformanceData() {
        try {
            // 获取内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            double usedMemoryMB = usedMemory / (1024.0 * 1024.0);
            
            currentSession.performance.recordMemoryUsage(usedMemoryMB);
            
            // CPU使用率需要通过其他方式获取，这里暂时跳过
            // currentSession.performance.recordCpuUsage(cpuUsage);
            
        } catch (Exception e) {
            AVLEngine.Logger.warn(TAG + ": Failed to sample performance data: " + e.getMessage());
        }
    }
}
