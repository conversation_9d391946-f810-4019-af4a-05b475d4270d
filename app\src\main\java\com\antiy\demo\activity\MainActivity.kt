package com.antiy.demo.activity

import FileUtils
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.DocumentsContract
import android.util.Log
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.callback.ScanListener
import com.antiy.avlsdk.config.Config
import com.antiy.avlsdk.entity.PipeMessage
import com.antiy.avlsdk.entity.ResultScan
import com.antiy.avlsdk.entity.ScanErrorType
import com.antiy.avlsdk.entity.ScanFileType
import com.antiy.avlsdk.scan.FileScanner
import com.antiy.avlsdk.storage.DBConstants.TABLE_CACHE
import com.antiy.avlsdk.storage.DataManager
import com.antiy.demo.App
import com.antiy.demo.LogAdapter
import com.antiy.demo.databinding.ActivityMainBinding
import java.io.File
import java.io.FileOutputStream
import kotlin.concurrent.thread


class MainActivity : AppCompatActivity() {
    private var scanFileFinishTime: Long = 0
    private var scanFileStartTime: Long = 0
    private var startStabilityTime: Long = 0L
    private lateinit var binding: ActivityMainBinding
    private var startTime = 0L
    private var endTime = 0L
    private var isScan24Hours: Boolean = false
    private val csvFilePath by lazy {
        File(getExternalFilesDir(null), "scan_results.csv").absolutePath
    }

    private val mAdapter by lazy {
        LogAdapter(arrayListOf())
    }

    // 创建文件管理权限请求启动器
    private lateinit var storagePermissionLauncher: ActivityResultLauncher<Intent>
    
    // 创建文件选择启动器
    private lateinit var filePickerLauncher: ActivityResultLauncher<Intent>
    private lateinit var dirPickerLauncher: ActivityResultLauncher<Intent>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        Log.e("CPU1", Build.CPU_ABI)

        initView()
        initListener()
        initCsvFile()
    }



    private fun initAvlConfig() {
        val whiteMap = HashMap<String, Boolean>().apply {
            this["1b88bc99186dbb0e705fe70b9176d8e34bde76dc573e5a1f43bc6fcfe4546b04"] = true
        }
        val scanList = arrayListOf(
            ScanFileType.JAR,ScanFileType.APK,ScanFileType.DEX,ScanFileType.MP3,ScanFileType.MP4,
            ScanFileType.JPG,ScanFileType.PNG,ScanFileType.GIF,ScanFileType.ELF
        )

        val sharedPrefs = getSharedPreferences("cloud_scan_settings", Context.MODE_PRIVATE)
        val cloudThreshold = sharedPrefs.getInt("cloud_threshold", 5000)
        val cloudSizeThresholdMb = sharedPrefs.getInt("cloud_size_threshold", 500)

        AVLEngine.getInstance().loadConfig(
            Config.Builder()
                .setBlackWhiteList(whiteMap)
                .setScanType(scanList)
                .setHistorySize(100)
                .setHistoryTimeout(7 * 24 * 60 * 60 * 1000)
                .setCloudCheckThreshold(cloudThreshold)
                .setCloudSizeThreshold(cloudSizeThresholdMb * 1024L * 1024L)
                .setSilentPerformanceThreshold(1)
                .build()
        )
    }

    private fun initView() {
        binding.rv.layoutManager = LinearLayoutManager(this)
        binding.rv.adapter = mAdapter
    }


    /**
     * 初始化AVLEngine
     * 此方法在获取到所有文件访问权限后调用
     */
    private fun initAVLEngine() {
        // 初始化AVLEngine
        val app = application as App
        if (!app.isAVLEngineInitialized()) {
            val uuid = app.initializeAVLEngine()

            // 检查SDK和统计功能状态
            val sdkInitialized = app.isAVLEngineInitialized()
            val statisticsEnabled = app.isStatisticsEnabled()

            val message = when {
                sdkInitialized && statisticsEnabled ->
                    "AVLEngine和统计功能初始化成功，UUID: ${uuid.substring(0, 8)}..."
                sdkInitialized ->
                    "AVLEngine初始化成功，但统计功能启用失败，UUID: ${uuid.substring(0, 8)}..."
                else ->
                    "AVLEngine初始化失败"
            }

            Toast.makeText(this, message, Toast.LENGTH_LONG).show()

            // 输出详细的统计状态信息到日志
            if (sdkInitialized) {
                Log.i("MainActivity", "统计功能状态: ${app.getStatisticsStatusInfo()}")
            }

            // 加载引擎配置
            initAvlConfig()
        }
    }

    private fun initListener() {
        binding.btnScan.setOnClickListener {
            isScan24Hours = false
//            DataManager.getInstance().clearAllData(TABLE_CACHE)
            scan()
        }

        binding.btnScanPause.setOnClickListener {
            AVLEngine.getInstance().scanPause()
        }

        binding.btnScanResume.setOnClickListener {
            AVLEngine.getInstance().scanResume()
        }

        binding.btnScanStop.setOnClickListener {
            AVLEngine.getInstance().scanStop()
        }

        binding.btnUpdateVirus.setOnClickListener {
            AVLEngine.getInstance().checkUpdate()
        }

        binding.btnScan24Hours.setOnClickListener {
            isScan24Hours = true
            startStabilityTime = System.currentTimeMillis()
            scan()
        }

        binding.btnHandUpdateVirus.setOnClickListener {
            openFilePicker()
        }

        binding.btnUuid.setOnClickListener {
            startActivity(Intent(this, UuidActivity::class.java))
        }

        binding.btnConcurrentScan.setOnClickListener {
            thread { FileScanner.scan("/sdcard/samples4000/FE4F8D8BAC1130C3F9300C39421ECAA7") }
//            thread { FileScanner.scan("/sdcard/samples/apk/1G/7A3FCB861B2C007A8D2FBC9DC370DA69") }
        }
        binding.btnSilentScan.setOnClickListener {
            scan(true)
        }
        binding.btnCpuPause.setOnClickListener {
            sendPipeMessage(PipeMessage.PAUSE)
        }
        binding.btnCpuContinue.setOnClickListener {
            sendPipeMessage(PipeMessage.CONTINUE)
        }
        binding.btnClearCache.setOnClickListener {
            DataManager.getInstance().clearAllData(TABLE_CACHE)
        }
    }

    private fun scan(silent: Boolean = false) {
        if (!FileUtils.hasStorageAccess(this)) {
            Toast.makeText(this, "需要存储权限才能扫描文件", Toast.LENGTH_SHORT).show()
            return
        }

        // 确保AVLEngine已初始化
        val app = application as App
        if (!app.isAVLEngineInitialized()) {
            Toast.makeText(this, "AVLEngine未初始化，请等待", Toast.LENGTH_SHORT).show()
            initAVLEngine()
            return
        }

        thread {
            val filePath = binding.etScanPath.text.toString()
            val file = File(filePath)

            if (!file.exists()) {
                runOnUiThread {
                    Toast.makeText(this, "文件路径不存在", Toast.LENGTH_SHORT).show()
                }
                return@thread
            }

            if (!file.canRead()) {
                runOnUiThread {
                    Toast.makeText(this, "无法读取文件，请检查权限", Toast.LENGTH_SHORT).show()
                }
                return@thread
            }

            if (file.isDirectory) {
                val fileCount = getDirectoryFileCount(file)
                AVLEngine.Logger.info("Found $fileCount files in directory")

                if (fileCount == 0) {
                    runOnUiThread {
                        Toast.makeText(this, "目录为空或无法访问", Toast.LENGTH_SHORT).show()
                    }
                    return@thread
                }

                startTime = System.currentTimeMillis()
                if (silent) {
                    AVLEngine.getInstance().scanDirSilent(filePath, createScanListener())
                } else {
                    AVLEngine.getInstance().scanDir(filePath, createScanListener())
                }
            } else {
                val scanResult = AVLEngine.getInstance().scanFile(file.path)
                runOnUiThread {
                    mAdapter.addData("单个文件扫描完成")
                }
            }
        }
    }

    private fun createScanListener() = object : ScanListener {
        override fun scanStart() {
            runOnUiThread {
                mAdapter.addData("开始扫描目录")
            }
        }

        override fun scanStop() {
            Log.e(MainActivity::class.java.simpleName, "扫描停止")
            runOnUiThread {
                mAdapter.addData("扫描终止")
            }
        }

        override fun scanFinish() {
            runOnUiThread {
                mAdapter.addData("目录扫描完成")
                endTime = System.currentTimeMillis()
                mAdapter.addData("耗时${endTime - startTime}ms")

                // 显示统计信息
                showStatisticsInfo()

                binding.rv.scrollToPosition(mAdapter.dataSet.size - 1)

                if (isScan24Hours && (System.currentTimeMillis() - startStabilityTime < 60 * 60 * 24 * 1000)) {
                    againScan()
                }
            }
        }

        override fun scanCount(count: Int) {
            runOnUiThread {
                val tip = "扫描总文件个数:$count 个"
                mAdapter.addData(tip)
                AVLEngine.Logger.info(tip)
            }
        }

        override fun scanFileStart(index: Int, path: String?) {
            runOnUiThread {
                scanFileStartTime = System.currentTimeMillis()
                mAdapter.addData("开始扫描第${index}文件,文件路径:$path")
            }
        }

        override fun scanFileFinish(index: Int, path: String?, result: ResultScan?) {
            AVLEngine.Logger.info("scanFileFinish:" + result.toString())
            path?.let { writeScanResultToCsv(it, result) }
            
            runOnUiThread {
                scanFileFinishTime = System.currentTimeMillis()
                mAdapter.addData(
                    "第${index}文件扫描完成,耗时：${scanFileFinishTime - scanFileStartTime} ms," +
                            "${if (result?.isMalicious == true) result.virusName else ""}"
                )
                binding.rv.scrollToPosition(mAdapter.dataSet.size - 1)
            }
        }

        override fun scanError(errorMsg: ScanErrorType?) {
            runOnUiThread {
                mAdapter.addData("扫描错误：${errorMsg?.name}")
                Toast.makeText(this@MainActivity, "当前有任务在扫描，请先停止上一个任务", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun getDirectoryFileCount(directory: File): Int {
        var count = 0
        directory.listFiles()?.forEach { file ->
            when {
                file.isFile -> count++
                file.isDirectory -> count += getDirectoryFileCount(file)
            }
        }
        return count
    }

    private fun againScan() {
        DataManager.getInstance().clearAllData(TABLE_CACHE)
        Thread.sleep(1000 * 5)
        scan()
    }

    /**
     * 显示统计信息
     */
    private fun showStatisticsInfo() {
        try {
            val app = application as App
            if (app.isStatisticsEnabled()) {
                // 显示今日统计摘要
                val todaySummary = com.antiy.demo.statistics.ScanStatisticsIntegration.getTodayStatisticsSummary()
                mAdapter.addData("今日统计: $todaySummary")

                // 显示最近扫描摘要
                val recentSummary = com.antiy.demo.statistics.ScanStatisticsIntegration.getRecentScansSummary(3)
                mAdapter.addData("最近扫描: $recentSummary")

                Log.i("MainActivity", "统计信息已显示")
            } else {
                mAdapter.addData("统计功能未启用")
                Log.w("MainActivity", "统计功能未启用")
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "显示统计信息失败", e)
            mAdapter.addData("统计信息获取失败: ${e.message}")
        }
    }

    /**
     * 打开文件选择器
     * 使用Activity Result API
     */
    private fun openFilePicker() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "*/*"
        }
        filePickerLauncher.launch(intent)
    }

    /**
     * 打开目录选择器
     * 使用Activity Result API
     */
    private fun openDirPicker() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT_TREE)
        dirPickerLauncher.launch(intent)
    }

    private fun uriToFilePath(context: Context, uri: Uri): String? {
        if (DocumentsContract.isDocumentUri(context, uri) && isExternalStorageDocument(uri)) {
            val docId = DocumentsContract.getDocumentId(uri)
            val split = docId.split(":")
            if ("primary".equals(split[0], ignoreCase = true)) {
                return "/sdcard/${split[1]}"
            }
        }
        return null
    }

    private fun isExternalStorageDocument(uri: Uri) =
        "com.android.externalstorage.documents" == uri.authority

    /**
     * 初始化CSV文件，如果文件不存在则创建并写入表头
     */
    private fun initCsvFile() {
        try {
            val file = File(csvFilePath)

            if (file.exists() && file.isDirectory) {
                file.delete()
            }

            if (!file.exists()) {
                file.parentFile?.mkdirs()
                file.createNewFile()
                // 写入CSV表头
                writeToCsv("文件路径,病毒名称\n")
            }
        } catch (e: Exception) {
            AVLEngine.Logger.error("Failed to initialize CSV file: ${e.message}")
        }
    }

    /**
     * 写入数据到CSV文件
     */
    private fun writeToCsv(content: String) {
        try {
            File(csvFilePath).appendText(content)
        } catch (e: Exception) {
            AVLEngine.Logger.error("Failed to write to CSV: ${e.message}")
        }
    }

    /**
     * 将扫描结果写入CSV文件
     */
    private fun writeScanResultToCsv(path: String, result: ResultScan?) {
        try {
            // 格式化CSV行数据
            val virusName = result?.virusName ?: ""

            // 处理路径中可能包含的逗号，用引号包裹
            val formattedPath = path.substring(path.lastIndexOf("/") + 1).split(".")[0]

            // 构建CSV行
            val csvLine = "$formattedPath,$virusName\n"
            
            // 写入文件
            writeToCsv(csvLine)
        } catch (e: Exception) {
            AVLEngine.Logger.error("Failed to write scan result to CSV: ${e.message}")
        }
    }

    /**
     * 给管道发送消息
     * @param msg
     */
    private fun sendPipeMessage(msg: PipeMessage) {
        try {
            val pipePath = <EMAIL>() + File.separator + "pipe";
            val file = File(pipePath)
            if (!file.exists()) {
                AVLEngine.Logger.error("pipe path is not exist:$pipePath")
                return
            }

            FileOutputStream(file).use { fos ->
                fos.write((msg.message + "\n").toByteArray())
                fos.flush()
            }
            AVLEngine.Logger.info("Pipe Message sent: " + msg.message)
        } catch (e: java.lang.Exception) {
            AVLEngine.Logger.error("Pipe Error sending message: " + e.message)
        }
    }
    companion object {
        private const val REQUEST_MANAGE_STORAGE = 1002
        private const val REQUEST_CODE = 10000
        private const val REQUEST_CODE_2 = 20000
    }
}
