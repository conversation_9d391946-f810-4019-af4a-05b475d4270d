[{"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_dialog_add_black_white_item.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\dialog_add_black_white_item.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_app_manage.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_app_manage.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_item_cache_management.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\item_cache_management.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_activity_scan_result.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\activity_scan_result.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_item_threat_detail.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\item_threat_detail.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_dialog_cache_size_setting.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\dialog_cache_size_setting.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_virus_scan.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_virus_scan.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_gray_rounded_button_background.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\gray_rounded_button_background.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_antivirus_engine.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_antivirus_engine.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_activity_main.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\activity_main.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_check_circle.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_check_circle.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_bg_app_logo.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\bg_app_logo.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_launcher_foreground.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_launcher_foreground.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_list_empty.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_list_empty.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_activity_antivirus_settings.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\activity_antivirus_settings.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_activity_scanning.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\activity_scanning.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_network.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_network.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\xml_data_extraction_rules.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\xml\\data_extraction_rules.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_info.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_info.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_rounded_button_background.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\rounded_button_background.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_app.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_app.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_virus_alert.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_virus_alert.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_more.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_more.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_performance_settings.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_performance_settings.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_language.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_language.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_circle_background_light_red.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\circle_background_light_red.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_quick_scan.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_quick_scan.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_whitelist.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_whitelist.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_delete.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_delete.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_arrow_back.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_arrow_back.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_performance.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_performance.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_refresh.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_refresh.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_deep_scan.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_deep_scan.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_circle_background_blue.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\circle_background_blue.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_warning.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_warning.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_cleanup.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_cleanup.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_logout.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_logout.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_item_black_white_list.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\item_black_white_list.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_scan_strategy.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_scan_strategy.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_activity_uuid.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\activity_uuid.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_check.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_check.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_activity_cache_management.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\activity_cache_management.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_scheduled_scan.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_scheduled_scan.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_fragment_scan.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\fragment_scan.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_item_scan_record.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\item_scan_record.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_help.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_help.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_activity_performance_settings.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\activity_performance_settings.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_activity_about.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\activity_about.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_fragment_home.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\fragment_home.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\xml_backup_rules.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\xml\\backup_rules.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_support.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_support.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_person.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_person.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_full_scan.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_full_scan.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_item_list.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\item_list.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_realtime_protection.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_realtime_protection.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_fragment_settings.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\fragment_settings.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_shield.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_shield.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_schedule.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_schedule.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_bg_info_card.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\bg_info_card.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_settings.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_settings.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\mipmap-hdpi_ic_launcher.webp.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_spinner_background.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\spinner_background.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_arrow_right.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_arrow_right.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\mipmap-mdpi_ic_launcher.webp.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_bg_circle_blue.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\bg_circle_blue.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_file.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_file.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_cloud.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_cloud.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_button_primary.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\button_primary.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_dark_mode.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_dark_mode.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_activity_cloud_scan_settings.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\activity_cloud_scan_settings.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\menu_bottom_nav_menu.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\menu\\bottom_nav_menu.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_play.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_play.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_item_scan_result.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\item_scan_result.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_fragment_protection.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\fragment_protection.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_circle_background_light_blue.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\circle_background_light_blue.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_shield_logo.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_shield_logo.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_fragment_virus_database.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\fragment_virus_database.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_virus_db.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_virus_db.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_email.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_email.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_notification.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_notification.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_scan.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_scan.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_home.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_home.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_custom_scan.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_custom_scan.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_privacy.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_privacy.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_protection.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_protection.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\color_bottom_nav_colors.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\color\\bottom_nav_colors.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_activity_help_feedback.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\activity_help_feedback.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_activity_black_white_list_management.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\activity_black_white_list_management.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_add.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_add.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_launcher_background.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_launcher_background.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_bg_edittext_rounded.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\bg_edittext_rounded.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\drawable_ic_back.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\drawable\\ic_back.xml"}, {"merged": "D:\\Projects\\Android\\cccj-sdk\\app\\build\\intermediates\\merged_res\\greatwallDebug\\layout_activity_home.xml.flat", "source": "D:\\Projects\\Android\\cccj-sdk\\app\\src\\main\\res\\layout\\activity_home.xml"}]