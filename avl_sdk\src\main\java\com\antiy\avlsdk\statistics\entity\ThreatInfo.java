package com.antiy.avlsdk.statistics.entity;

/**
 * 威胁信息
 * 记录检测到的威胁详细信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ThreatInfo {
    
    /** 文件路径 */
    public String filePath;
    
    /** 文件名 */
    public String fileName;
    
    /** 病毒名称 */
    public String virusName;
    
    /** 文件SHA256值 */
    public String sha256;
    
    /** 文件大小（字节） */
    public long fileSize;
    
    /** 检测时间戳（毫秒） */
    public long detectedTime;
    
    /** 检测引擎：mobile/pc/cloud */
    public String engine;
    
    /** 是否为云查结果 */
    public boolean isCloudScan;
    
    /** 扫描耗时（毫秒） */
    public long scanTime;
    
    /** 威胁级别：HIGH/MEDIUM/LOW */
    public String riskLevel;
    
    /** 威胁描述 */
    public String description;
    
    /** 处理状态：DETECTED/QUARANTINED/DELETED/IGNORED */
    public String actionStatus;
    
    public ThreatInfo() {
        this.detectedTime = System.currentTimeMillis();
        this.actionStatus = "DETECTED";
        this.riskLevel = "MEDIUM";
    }
    
    public ThreatInfo(String filePath, String virusName, String sha256, String engine) {
        this();
        this.filePath = filePath;
        this.virusName = virusName;
        this.sha256 = sha256;
        this.engine = engine;
        
        // 从文件路径提取文件名
        if (filePath != null && !filePath.isEmpty()) {
            int lastSlash = filePath.lastIndexOf('/');
            if (lastSlash >= 0 && lastSlash < filePath.length() - 1) {
                this.fileName = filePath.substring(lastSlash + 1);
            } else {
                this.fileName = filePath;
            }
        }
    }
    
    /**
     * 从ResultScan创建ThreatInfo
     */
    public static ThreatInfo fromResultScan(String filePath, com.antiy.avlsdk.entity.ResultScan result, String engine, long scanTime) {
        if (result == null || !result.isMalicious) {
            return null;
        }
        
        ThreatInfo threat = new ThreatInfo(filePath, result.virusName, result.sha256sum, engine);
        threat.isCloudScan = result.isCloudScan;
        threat.scanTime = scanTime;
        
        // 根据病毒名称推断威胁级别
        threat.riskLevel = inferRiskLevel(result.virusName);
        
        return threat;
    }
    
    /**
     * 根据病毒名称推断威胁级别
     */
    private static String inferRiskLevel(String virusName) {
        if (virusName == null || virusName.isEmpty()) {
            return "LOW";
        }
        
        String lowerName = virusName.toLowerCase();
        
        // 高风险关键词
        if (lowerName.contains("trojan") || lowerName.contains("backdoor") || 
            lowerName.contains("rootkit") || lowerName.contains("ransomware")) {
            return "HIGH";
        }
        
        // 中风险关键词
        if (lowerName.contains("adware") || lowerName.contains("spyware") || 
            lowerName.contains("malware") || lowerName.contains("virus")) {
            return "MEDIUM";
        }
        
        // 默认低风险
        return "LOW";
    }
    
    /**
     * 获取威胁级别的中文描述
     */
    public String getRiskLevelText() {
        switch (riskLevel) {
            case "HIGH": return "高风险";
            case "MEDIUM": return "中风险";
            case "LOW": return "低风险";
            default: return "未知";
        }
    }
    
    /**
     * 获取引擎的中文描述
     */
    public String getEngineText() {
        switch (engine) {
            case "mobile": return "移动引擎";
            case "pc": return "PC引擎";
            case "cloud": return "云查引擎";
            default: return "未知引擎";
        }
    }
    
    /**
     * 获取处理状态的中文描述
     */
    public String getActionStatusText() {
        switch (actionStatus) {
            case "DETECTED": return "已检出";
            case "QUARANTINED": return "已隔离";
            case "DELETED": return "已删除";
            case "IGNORED": return "已忽略";
            default: return "未知状态";
        }
    }
    
    /**
     * 设置文件大小
     */
    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }
    
    /**
     * 设置威胁描述
     */
    public void setDescription(String description) {
        this.description = description;
    }
    
    /**
     * 标记为已隔离
     */
    public void markQuarantined() {
        this.actionStatus = "QUARANTINED";
    }
    
    /**
     * 标记为已删除
     */
    public void markDeleted() {
        this.actionStatus = "DELETED";
    }
    
    /**
     * 标记为已忽略
     */
    public void markIgnored() {
        this.actionStatus = "IGNORED";
    }
    
    /**
     * 检查是否为高风险威胁
     */
    public boolean isHighRisk() {
        return "HIGH".equals(riskLevel);
    }
    
    /**
     * 检查是否已处理
     */
    public boolean isHandled() {
        return "QUARANTINED".equals(actionStatus) || "DELETED".equals(actionStatus);
    }
    
    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSize <= 0) {
            return "未知大小";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    @Override
    public String toString() {
        return "ThreatInfo{" +
                "fileName='" + fileName + '\'' +
                ", virusName='" + virusName + '\'' +
                ", engine='" + engine + '\'' +
                ", riskLevel='" + riskLevel + '\'' +
                ", fileSize=" + getFormattedFileSize() +
                ", scanTime=" + scanTime + "ms" +
                '}';
    }
}
