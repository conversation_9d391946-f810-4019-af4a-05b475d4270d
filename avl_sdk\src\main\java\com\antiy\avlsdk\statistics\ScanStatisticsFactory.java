package com.antiy.avlsdk.statistics;

import android.content.Context;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.statistics.entity.ScanSessionRecord;

import java.util.List;

/**
 * 扫描统计工厂类
 * 提供便捷的方法来创建和管理扫描统计功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ScanStatisticsFactory {
    
    private static final String TAG = "ScanStatisticsFactory";
    
    /** 统计管理器实例 */
    private static ScanStatisticsManager statisticsManager;
    
    /** 是否已初始化 */
    private static boolean initialized = false;
    
    /**
     * 初始化扫描统计功能
     */
    public static boolean initialize(Context context) {
        if (initialized) {
            return true;
        }
        
        try {
            statisticsManager = ScanStatisticsManager.getInstance();
            boolean result = statisticsManager.initialize(context);
            
            if (result) {
                initialized = true;
                AVLEngine.Logger.info(TAG + ": Scan statistics initialized successfully");
            } else {
                AVLEngine.Logger.error(TAG + ": Failed to initialize scan statistics");
            }
            
            return result;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Exception during initialization: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 创建统计感知的扫描监听器
     * 
     * @param originalListener 原始的扫描监听器
     * @param scanType 扫描类型（LOCAL/CLOUD/MIXED）
     * @param scanPath 扫描路径
     * @return 包装后的监听器，如果统计功能未初始化则返回原始监听器
     */
    public static ScanListener createStatisticsAwareListener(ScanListener originalListener, String scanType, String scanPath) {
        if (!initialized || statisticsManager == null) {
            AVLEngine.Logger.warn(TAG + ": Statistics not initialized, returning original listener");
            return originalListener;
        }
        
        try {
            StatisticsAwareScanListener wrapper = new StatisticsAwareScanListener(originalListener, statisticsManager);
            wrapper.setScanParameters(scanType, scanPath);
            
            AVLEngine.Logger.info(TAG + ": Created statistics-aware listener for scan type: " + scanType);
            return wrapper;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to create statistics-aware listener: " + e.getMessage());
            return originalListener;
        }
    }
    
    /**
     * 创建统计感知的扫描监听器（使用默认参数）
     */
    public static ScanListener createStatisticsAwareListener(ScanListener originalListener) {
        return createStatisticsAwareListener(originalListener, "LOCAL", "");
    }
    
    /**
     * 获取统计管理器实例
     */
    public static ScanStatisticsManager getStatisticsManager() {
        if (!initialized) {
            AVLEngine.Logger.warn(TAG + ": Statistics not initialized");
            return null;
        }
        return statisticsManager;
    }
    
    /**
     * 检查统计功能是否已初始化
     */
    public static boolean isInitialized() {
        return initialized;
    }
    
    /**
     * 获取最近的扫描记录摘要
     */
    public static String getRecentScansSummary(int limit) {
        if (!initialized || statisticsManager == null) {
            return "统计功能未初始化";
        }
        
        try {
            List<ScanSessionRecord> recentScans = statisticsManager.getRecentScans(limit);
            if (recentScans == null || recentScans.isEmpty()) {
                return "暂无扫描记录";
            }

            StringBuilder summary = new StringBuilder();
            summary.append("=== 最近 ").append(recentScans.size()).append(" 次扫描记录 ===\n");

            for (int i = 0; i < recentScans.size(); i++) {
                ScanSessionRecord record = recentScans.get(i);
                summary.append(String.format("%d. %s\n", i + 1, record.getSummary()));
            }
            
            return summary.toString();
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get recent scans summary: " + e.getMessage());
            return "获取扫描记录失败: " + e.getMessage();
        }
    }
    
    /**
     * 获取今日扫描统计
     */
    public static String getTodayStatisticsSummary() {
        if (!initialized || statisticsManager == null) {
            return "统计功能未初始化";
        }
        
        try {
            String today = new java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
                    .format(new java.util.Date());

            ScanStatisticsManager.DailyStatistics dailyStats = statisticsManager.getDailyStatistics(today);
            if (dailyStats == null) {
                return "获取今日统计失败";
            }
            
            if (dailyStats.totalScans == 0) {
                return "今日暂无扫描记录";
            }
            
            return String.format("=== 今日扫描统计 (%s) ===\n" +
                            "扫描次数: %d 次\n" +
                            "成功扫描: %d 次\n" +
                            "扫描文件: %d 个\n" +
                            "发现威胁: %d 个\n" +
                            "平均耗时: %.1f 秒",
                    dailyStats.date,
                    dailyStats.totalScans,
                    dailyStats.successfulScans,
                    dailyStats.totalFiles,
                    dailyStats.totalThreats,
                    dailyStats.avgScanTime / 1000.0);
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get today statistics: " + e.getMessage());
            return "获取今日统计失败: " + e.getMessage();
        }
    }
    
    /**
     * 获取存储使用情况
     */
    public static String getStorageUsageSummary() {
        if (!initialized || statisticsManager == null) {
            return "统计功能未初始化";
        }
        
        try {
            ScanRecordManager.StorageStats storageStats = statisticsManager.getStorageStats();
            if (storageStats == null) {
                return "获取存储统计失败";
            }
            
            return String.format("=== 存储使用情况 ===\n" +
                            "记录总数: %d 条\n" +
                            "存储空间: %s\n" +
                            "最早记录: %s\n" +
                            "最新记录: %s",
                    storageStats.totalRecords,
                    storageStats.getFormattedSize(),
                    storageStats.earliestRecordTime > 0 ? 
                            new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault())
                                    .format(new java.util.Date(storageStats.earliestRecordTime)) : "无",
                    storageStats.latestRecordTime > 0 ? 
                            new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault())
                                    .format(new java.util.Date(storageStats.latestRecordTime)) : "无");
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get storage usage: " + e.getMessage());
            return "获取存储统计失败: " + e.getMessage();
        }
    }
    
    /**
     * 执行清理过期记录
     */
    public static String cleanupExpiredRecords() {
        if (!initialized || statisticsManager == null) {
            return "统计功能未初始化";
        }
        
        try {
            ScanRecordManager.StorageStats beforeStats = statisticsManager.getStorageStats();
            statisticsManager.cleanupExpiredRecords();
            ScanRecordManager.StorageStats afterStats = statisticsManager.getStorageStats();
            
            int deletedRecords = beforeStats.totalRecords - afterStats.totalRecords;
            long freedSpace = beforeStats.totalSizeBytes - afterStats.totalSizeBytes;
            
            return String.format("清理完成：删除 %d 条过期记录，释放 %.1f KB 空间",
                    deletedRecords, freedSpace / 1024.0);
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to cleanup expired records: " + e.getMessage());
            return "清理失败: " + e.getMessage();
        }
    }
    
    /**
     * 导出统计报告
     */
    public static String exportStatisticsReport(String outputPath, int days) {
        if (!initialized || statisticsManager == null) {
            return "统计功能未初始化";
        }
        
        try {
            long endTime = System.currentTimeMillis();
            long startTime = endTime - (days * 24 * 60 * 60 * 1000L);
            
            String result = statisticsManager.exportStatisticsReport(startTime, endTime, outputPath);
            
            if (result != null) {
                return "统计报告已导出到: " + result;
            } else {
                return "导出统计报告失败";
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to export statistics report: " + e.getMessage());
            return "导出失败: " + e.getMessage();
        }
    }
    
    /**
     * 输出统计功能状态
     */
    public static void logStatisticsStatus() {
        AVLEngine.Logger.info(TAG + ": === 扫描统计功能状态 ===");
        AVLEngine.Logger.info(TAG + ": 初始化状态: " + (initialized ? "已初始化" : "未初始化"));
        
        if (initialized && statisticsManager != null) {
            AVLEngine.Logger.info(TAG + ": " + getStorageUsageSummary().replace("\n", " | "));
            AVLEngine.Logger.info(TAG + ": " + getTodayStatisticsSummary().replace("\n", " | "));
        }
        
        AVLEngine.Logger.info(TAG + ": ========================");
    }
}
