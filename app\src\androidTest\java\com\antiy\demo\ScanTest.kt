package com.antiy.demo

import android.content.Context
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.callback.ScanListener
import com.antiy.avlsdk.config.Config
import com.antiy.avlsdk.entity.ResultScan
import com.antiy.avlsdk.entity.ScanFileType
import com.antiy.avlsdk.scan.*
import com.antiy.avlsdk.storage.DBConstants
import com.antiy.avlsdk.storage.DataManager
import com.antiy.avlsdk.utils.SdkConst
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.io.File
import java.io.FileOutputStream
import java.util.UUID
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

@RunWith(AndroidJUnit4::class)
class ScanTest {
    private lateinit var context: Context
    private lateinit var dataManager: DataManager

    @Before
    fun setUp() {
        context = InstrumentationRegistry.getInstrumentation().targetContext
        val uuid = UUID.randomUUID().toString()
        AVLEngine.init(context, uuid, ALog(context), NetworkManager())
        dataManager = DataManager.getInstance()
        
        // 清理测试目录和缓存
        cleanTestEnvironment()
    }

    /**
     * 测试文件类型检查功能
     * 验证：
     * 1. 所有支持的文件类型
     * 2. 不支持的文件类型
     * 3. 无扩展名文件
     * 4. 特殊字符文件名
     */
    @Test
    fun testFileTypeCheck() {
        val fileChecker = FileChecker.getInstance()
        AVLEngine.getInstance().loadConfig(Config.Builder()
            .setScanType(arrayListOf(
                ScanFileType.JAR, ScanFileType.DEX, ScanFileType.APK, ScanFileType.MP3, ScanFileType.MP4, ScanFileType.JPG,
                ScanFileType.PNG, ScanFileType.GIF, ScanFileType.ELF
            ))
            .build())

        // 测试有扩展名的文件
        val apkFile = createTestFile("test.apk", "some content")
        assertTrue("扩展名在列表中应该返回 true", fileChecker.isValidType(apkFile))
        
        val txtFile = createTestFile("test.txt", "some content")
        assertFalse("扩展名不在列表中应该返回 false", fileChecker.isValidType(txtFile))

        // 测试无扩展名但内容有效的文件
        val noExtApk = createTestFile("test_no_ext", "PK\u0003\u0004")
        assertTrue("无扩展名但内容有效应该返回 true", fileChecker.isValidType(noExtApk))
        
        val noExtInvalid = createTestFile("test_no_ext_invalid", "invalid content")
        assertFalse("无扩展名且内容无效应该返回 false", fileChecker.isValidType(noExtInvalid))

        // 清理测试文件
        apkFile.delete()
        txtFile.delete()
        noExtApk.delete()
        noExtInvalid.delete()
    }

    /**
     * 测试缓存管理功能
     * 验证：
     * 1. 缓存存储和读取
     * 2. 缓存键生成规则
     * 3. 文件修改后缓存失效
     */
    @Test
    fun testCacheManagement() {
        val testFile = createTestFile("test.apk", "PK")
        val path = testFile.absolutePath
        
        // 测试无缓存情况
        assertFalse("新文件不应该有缓存", CacheManager.hasCache(path))
        
        // 存储扫描结果
        CacheManager.storeScanResult(path, "test_virus","")
        
        // 验证缓存存在
        assertTrue("应该找到缓存", CacheManager.hasCache(path))
        val cache = CacheManager.getCacheResult(path)
        assertNotNull("应该能获取缓存", cache)
        assertEquals("test_virus", cache?.virusName)
        
        // 修改文件后验证缓存失效
        testFile.setLastModified(System.currentTimeMillis() + 1000)
        assertFalse("文件修改后缓存应该失效", CacheManager.hasCache(path))
    }

    /**
     * 测试黑白名单功能
     * 验证：
     * 1. 黑名单匹配
     * 2. 白名单匹配
     * 3. 不在名单中的文件
     */
    @Test
    fun testBlackWhiteList() {
        val blackWhiteManager = BlackWhiteManager.getInstance()
        val testFile = createTestFile("test.apk", "PK")
        val filePath = testFile.absolutePath
        
        // 添加测试数据到黑白名单
        val blackWhiteMap = HashMap<String, Boolean>().apply {
            put(filePath, true) // true 表示加入黑名单
        }
        
        // 直接使用 DataManager 保存数据
        dataManager.clearBlackWhiteList() // 先清理历史数据
        dataManager.saveBlackWhiteList(blackWhiteMap)
        
        // 添加日志检查数据是否保存成功
        val savedMap = dataManager.getBlackWhiteListData()
        AVLEngine.Logger.info("Saved black/white list: $savedMap")
        AVLEngine.Logger.info("Looking for path: $filePath")
        
        // 测试黑名单匹配
        val result = blackWhiteManager.match(filePath)
        AVLEngine.Logger.info("Match result: $result")
        
        // 验证结果
        assertNotNull("应该找到黑名单记录", result)
        assertTrue("应该是黑名单", result?.isBlack == true)
        
        // 测试白名单
        val whiteFile = createTestFile("white.apk", "PK")
        val whiteFilePath = whiteFile.absolutePath
        
        blackWhiteMap.clear()
        blackWhiteMap[whiteFilePath] = false // false 表示加入白名单
        dataManager.saveBlackWhiteList(blackWhiteMap)
        
        AVLEngine.Logger.info("White file path: $whiteFilePath")
        val whiteResult = blackWhiteManager.match(whiteFilePath)
        AVLEngine.Logger.info("White match result: $whiteResult")
        
        assertNotNull("应该找到白名单记录", whiteResult)
        assertFalse("应该是白名单", whiteResult?.isBlack == true)
        
        // 测试不在名单中的文件
        val normalFile = createTestFile("normal.apk", "PK")
        val normalResult = blackWhiteManager.match(normalFile.absolutePath)
        assertNull("不应该找到记录", normalResult)
        
        // 清理测试文件
        testFile.delete()
        whiteFile.delete()
        normalFile.delete()
    }

    /**
     * 测试文件扫描功能
     * 验证：
     * 1. 正常文件扫描
     * 2. 不存在的文件
     * 3. 超大文件
     * 4. 不同类型文件的扫描引擎选择
     */
    @Test
    fun testFileScan() {
        // 测试不存在的文件
        val nonExistResult = FileScanner.scan("/nonexistent/path")
        assertFalse("不存在的文件应该返回失败", nonExistResult.isMalicious)
        assertEquals("文件不存在可能已删除", nonExistResult.errMsg)
        
        // 测试正常 APK 文件
        val apkFile = createTestFile("test.apk", "PK")
        val apkResult = FileScanner.scan(apkFile.absolutePath)
        assertNotNull("应该返回扫描结果", apkResult)
        
        // 测试超大文件
        val largeFile = createLargeFile("large.mp4")
        val largeResult = FileScanner.scan(largeFile.absolutePath)
        assertNotNull("超大文件应该返回结果", largeResult)
    }

    /**
     * 测试文件夹扫描功能
     * 验证：
     * 1. 空文件夹
     * 2. 多文件文件夹
     * 3. 递归扫描
     * 4. 扫描进度回调
     */
    /*@Test
    fun testFolderScan() {
        // 创建测试目录结构
        val testDir = File("/sdcard/samples/apk/100")
        val latch = CountDownLatch(1)
        var scanComplete = false

        var currentIndex = 0
        
        val listener = object : ScanListener {
            override fun scanStart() {
            }

            override fun scanStop() {

            }

            override fun scanFinish() {
                latch.countDown()
            }

            override fun scanCount(count: Int) {
            }

            override fun scanFileStart(index: Int, path: String?) {
            }

            override fun scanFileFinish(index: Int, path: String?, result: ResultScan?) {

            }
        }

        FolderScanner.scanFolder(testDir.absolutePath, listener)
        latch.await()
        // 等待扫描完成
        assertTrue("扫描应该完成", scanComplete)
    }*/

    /**
     * 测试云扫描功能
     * 验证：
     * 1. 单文件云扫描
     * 2. 批量文件云扫描
     * 3. 网络超时处理
     * 4. 暂停/继续/停止功能
     */
    @Test
    fun testCloudScanner() {
        /*val cloudScanner = CloudScanner()
        val testFiles = createTestFiles(20)
        val latch = CountDownLatch(1)
        var scanComplete = false
        
        val listener = object : ScanListener {
            override fun scanStart() {
                // 记录开始
            }

            override fun scanStop() {
                latch.countDown()
            }

            override fun scanFinish() {
                scanComplete = true
                latch.countDown()
            }

            override fun scanCount(count: Int) {
                assertTrue(count > 0)
            }

            override fun scanFileStart(index: Int, path: String?) {
                assertNotNull(path)
            }

            override fun scanFileFinish(index: Int, path: String?, result: ResultScan?) {
                assertNotNull(path)
            }
        }

        // 确保在开始新扫描前停止之前的扫描
        cloudScanner.stop()
        Thread.sleep(1000) // 给足够时间完成停止操作
        
        // 开始新的扫描
        cloudScanner.scan(testFiles.map { it.absolutePath }, listener)
        
        // 等待扫描完成
        assertTrue("扫描应该在合理时间内完成", latch.await(60, TimeUnit.SECONDS))
        assertTrue("扫描应该正常完成", scanComplete)
        
        // 清理测试文件
        testFiles.forEach { it.delete() }*/
    }

    @Test
    fun testInvalidApkFiles() {
        // 测试各种无的 APK 文件
        val testCases = listOf(
            Pair("empty.apk", ByteArray(0)),
            Pair("invalid_zip.apk", "Not a ZIP file".toByteArray()),
            Pair("corrupted.apk", ByteArray(1024) { (it % 256).toByte() })
        )
        
        testCases.forEach { (name, content) ->
            val file = createTestFile(name, content)
            val result = AVLEngine.getInstance().scanFile(file.absolutePath)
            assertFalse("无效的 APK 文件应该被正确识别：$name", result.isMalicious)
            file.delete()
        }
    }

    /**
     * 测试文件魔数检测功能
     * 验证：
     * 1. 所有支持的文件类型
     * 2. 无效的文件内容
     * 3. 边界情况
     */
    @Test
    fun testMagicNumberDetection() {
        val fileChecker = FileChecker.getInstance()

        val jarFile = File("/sdcard/MagicNumberTest/jar")
        assertTrue("应该识别出有效的 JAR 文件",fileChecker.isValidType(jarFile))
        
        // 1. 测试APK/ZIP文件
        val apkFile = File("/sdcard/MagicNumberTest/apk")
        assertTrue("应该识别出有效的 APK 文件", fileChecker.isValidType(apkFile))

        // 2. 测试 DEX 文件
        val dexFile = File("/sdcard/MagicNumberTest/dex")
        assertTrue("应该识别出 DEX 文件", fileChecker.isValidType(dexFile))

        // 3. 测试 JPG 文件
        val jpgFile = File("/sdcard/MagicNumberTest/jpg")
        assertTrue("应该识别出 JPG 文件", fileChecker.isValidType(jpgFile))

        // 4. 测试 PNG 文件
        val pngFile = File("/sdcard/MagicNumberTest/png")
        assertTrue("应该识别出 PNG 文件", fileChecker.isValidType(pngFile))

        // 5. 测试 GIF 文件
        val gif87aFile = File("/sdcard/MagicNumberTest/gif")
        assertTrue("应该识别出 GIF87a 文件", fileChecker.isValidType(gif87aFile))

        val mp3File = File("/sdcard/MagicNumberTest/mp3")
        assertTrue("应该识别出 MP3 文件", fileChecker.isValidType(mp3File))

        // 7. 测试 MP4 文件
        val mp4File = File("/sdcard/MagicNumberTest/mp4")
        assertTrue("应该识别出 MP4 文件", fileChecker.isValidType(mp4File))

        // 8. 测试 ELF 文件
        val elfFile = File("/sdcard/MagicNumberTest/elf")
        assertTrue("应该识别出 ELF 文件", fileChecker.isValidType(elfFile))

        // 9. 测试边界情况
        // 9.1 空文件
        val emptyFile = createTestFile("test_empty", byteArrayOf())
        assertFalse("空文件应该被拒绝", fileChecker.isValidType(emptyFile))

        // 9.2 文件太小
        val tooSmallFile = createTestFile("test_small", byteArrayOf(0x50, 0x4B, 0x03))
        assertFalse("太小的文件应该被拒绝", fileChecker.isValidType(tooSmallFile))

        // 9.3 无效的魔数
        val invalidFile = createTestFile("test_invalid", byteArrayOf(
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
        ))
        assertFalse("无效的魔数应该被拒绝", fileChecker.isValidType(invalidFile))

        // 9.4 文件不存在
        val nonExistentFile = File(context.filesDir, "nonexistent")
        assertFalse("不存在的文件应该被拒绝", fileChecker.isValidType(nonExistentFile))

        // 10. 清理测试文件
//        apkFile.delete()
//        dexFile.delete()
//        jpgFile.delete()
//        pngFile.delete()
//        gif87aFile.delete()
//        gif89aFile.delete()
//        mp3File.delete()
//        mp4File.delete()
//        elfFile.delete()
        emptyFile.delete()
        tooSmallFile.delete()
        invalidFile.delete()
    }

    // 辅助方法

    private fun cleanTestEnvironment() {
        File(context.filesDir, "test_scans").deleteRecursively()
        dataManager.clearAllData(DBConstants.TABLE_CACHE)
        dataManager.clearBlackWhiteList()
    }

    private fun createTestFile(name: String, content: String): File {
        return createTestFile(name, content.toByteArray())
    }

    private fun createTestFile(name: String, content: ByteArray): File {
        val file = File(context.filesDir, "test_scans/$name")
        file.parentFile?.mkdirs()
        FileOutputStream(file).use { 
            it.write(content)
        }
        return file
    }

    private fun createLargeFile(name: String): File {
        val file = File(context.filesDir, "test_scans/$name")
        file.parentFile?.mkdirs()
        FileOutputStream(file).use { output ->
            // 创建一个超过云查阈值的文件
            val buffer = ByteArray(1024)
            repeat(dataManager.getCloudSizeThreshold().toInt() / 1024 + 1) {
                output.write(buffer)
            }
        }
        return file
    }

    private fun createTestDirectory(): File {
        val dir = File(context.filesDir, "test_scans/test_dir")
        dir.mkdirs()
        createTestFile("test_dir/test1.apk", "PK")
        createTestFile("test_dir/test2.jpg", byteArrayOf(0xFF.toByte(), 0xD8.toByte()))
        createTestFile("test_dir/subdir/test3.mp4", "....ftyp")
        return dir
    }

    private fun createTestFiles(count: Int): List<File> {
        return (1..count).map { 
            createTestFile("test$it.apk", "PK")
        }
    }
} 