# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 58ms
  [gap of 19ms]
create_cxx_tasks completed in 78ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 73ms
create_cxx_tasks completed in 76ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 65ms
create_cxx_tasks completed in 74ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 18ms]
    create-ARM64_V8A-model 10ms
    [gap of 49ms]
  create-initial-cxx-model completed in 77ms
create_cxx_tasks completed in 85ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 56ms
create_cxx_tasks completed in 64ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 33ms
create_cxx_tasks completed in 34ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 24ms
    [gap of 13ms]
    create-ARM64_V8A-model 10ms
    create-X86-model 12ms
    create-module-model 21ms
    [gap of 11ms]
    create-variant-model 34ms
    create-ARM64_V8A-model 16ms
    create-X86-model 10ms
    create-X86_64-model 18ms
  create-initial-cxx-model completed in 193ms
  [gap of 15ms]
create_cxx_tasks completed in 209ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 49ms
create_cxx_tasks completed in 59ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 76ms
  [gap of 14ms]
create_cxx_tasks completed in 90ms

