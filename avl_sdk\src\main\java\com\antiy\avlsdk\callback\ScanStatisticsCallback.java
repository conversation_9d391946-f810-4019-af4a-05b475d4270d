package com.antiy.avlsdk.callback;

import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.entity.ScanErrorType;
import com.antiy.avlsdk.entity.ScanSessionSummary;

/**
 * 扫描统计回调接口
 * 
 * 该接口定义了扫描过程中统计数据收集的回调方法。
 * SDK在扫描过程中会在适当的时机调用这些方法，
 * 应用层可以实现此接口来收集和处理统计数据。
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface ScanStatisticsCallback {
    
    /**
     * 扫描会话开始
     * 
     * 当开始一个新的扫描会话时调用此方法。
     * 
     * @param sessionId 会话唯一标识符
     * @param scanType 扫描类型 (LOCAL/CLOUD/MIXED)
     * @param scanPath 扫描路径
     * @param startTime 开始时间戳（毫秒）
     */
    void onScanSessionStart(String sessionId, String scanType, String scanPath, long startTime);
    
    /**
     * 扫描进度更新
     * 
     * 当扫描进度发生变化时调用此方法。
     * 
     * @param sessionId 会话唯一标识符
     * @param totalFiles 总文件数
     * @param scannedFiles 已扫描文件数
     */
    void onScanProgress(String sessionId, int totalFiles, int scannedFiles);
    
    /**
     * 单个文件扫描完成
     * 
     * 当完成单个文件的扫描时调用此方法。
     * 
     * @param sessionId 会话唯一标识符
     * @param fileIndex 文件索引（从0开始）
     * @param filePath 文件路径
     * @param result 扫描结果
     * @param scanTimeMs 扫描耗时（毫秒）
     * @param engineType 使用的引擎类型 (MOBILE/PC/CLOUD)
     */
    void onFileScanned(String sessionId, int fileIndex, String filePath, 
                      ResultScan result, long scanTimeMs, String engineType);
    
    /**
     * 扫描会话完成
     * 
     * 当扫描会话正常完成时调用此方法。
     * 
     * @param sessionId 会话唯一标识符
     * @param summary 会话摘要信息
     */
    void onScanSessionComplete(String sessionId, ScanSessionSummary summary);
    
    /**
     * 扫描会话失败
     * 
     * 当扫描会话因错误而失败时调用此方法。
     * 
     * @param sessionId 会话唯一标识符
     * @param errorType 错误类型
     * @param errorMessage 错误消息
     */
    void onScanSessionFailed(String sessionId, ScanErrorType errorType, String errorMessage);
    
    /**
     * 扫描会话取消
     * 
     * 当扫描会话被用户取消时调用此方法。
     * 
     * @param sessionId 会话唯一标识符
     */
    void onScanSessionCancelled(String sessionId);
}
