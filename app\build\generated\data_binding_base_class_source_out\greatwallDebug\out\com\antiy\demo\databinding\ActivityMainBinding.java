// Generated by view binder compiler. Do not edit!
package com.antiy.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.antiy.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnClearCache;

  @NonNull
  public final Button btnConcurrentScan;

  @NonNull
  public final Button btnCpuContinue;

  @NonNull
  public final Button btnCpuPause;

  @NonNull
  public final Button btnHandUpdateVirus;

  @NonNull
  public final Button btnScan;

  @NonNull
  public final Button btnScan24Hours;

  @NonNull
  public final Button btnScanPause;

  @NonNull
  public final Button btnScanResume;

  @NonNull
  public final Button btnScanStop;

  @NonNull
  public final Button btnSilentScan;

  @NonNull
  public final Button btnUpdateVirus;

  @NonNull
  public final Button btnUuid;

  @NonNull
  public final EditText etScanPath;

  @NonNull
  public final ConstraintLayout main;

  @NonNull
  public final RecyclerView rv;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView, @NonNull Button btnClearCache,
      @NonNull Button btnConcurrentScan, @NonNull Button btnCpuContinue,
      @NonNull Button btnCpuPause, @NonNull Button btnHandUpdateVirus, @NonNull Button btnScan,
      @NonNull Button btnScan24Hours, @NonNull Button btnScanPause, @NonNull Button btnScanResume,
      @NonNull Button btnScanStop, @NonNull Button btnSilentScan, @NonNull Button btnUpdateVirus,
      @NonNull Button btnUuid, @NonNull EditText etScanPath, @NonNull ConstraintLayout main,
      @NonNull RecyclerView rv) {
    this.rootView = rootView;
    this.btnClearCache = btnClearCache;
    this.btnConcurrentScan = btnConcurrentScan;
    this.btnCpuContinue = btnCpuContinue;
    this.btnCpuPause = btnCpuPause;
    this.btnHandUpdateVirus = btnHandUpdateVirus;
    this.btnScan = btnScan;
    this.btnScan24Hours = btnScan24Hours;
    this.btnScanPause = btnScanPause;
    this.btnScanResume = btnScanResume;
    this.btnScanStop = btnScanStop;
    this.btnSilentScan = btnSilentScan;
    this.btnUpdateVirus = btnUpdateVirus;
    this.btnUuid = btnUuid;
    this.etScanPath = etScanPath;
    this.main = main;
    this.rv = rv;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_clear_cache;
      Button btnClearCache = ViewBindings.findChildViewById(rootView, id);
      if (btnClearCache == null) {
        break missingId;
      }

      id = R.id.btn_concurrent_scan;
      Button btnConcurrentScan = ViewBindings.findChildViewById(rootView, id);
      if (btnConcurrentScan == null) {
        break missingId;
      }

      id = R.id.btn_cpu_continue;
      Button btnCpuContinue = ViewBindings.findChildViewById(rootView, id);
      if (btnCpuContinue == null) {
        break missingId;
      }

      id = R.id.btn_cpu_pause;
      Button btnCpuPause = ViewBindings.findChildViewById(rootView, id);
      if (btnCpuPause == null) {
        break missingId;
      }

      id = R.id.btn_hand_update_virus;
      Button btnHandUpdateVirus = ViewBindings.findChildViewById(rootView, id);
      if (btnHandUpdateVirus == null) {
        break missingId;
      }

      id = R.id.btn_scan;
      Button btnScan = ViewBindings.findChildViewById(rootView, id);
      if (btnScan == null) {
        break missingId;
      }

      id = R.id.btn_scan_24_hours;
      Button btnScan24Hours = ViewBindings.findChildViewById(rootView, id);
      if (btnScan24Hours == null) {
        break missingId;
      }

      id = R.id.btn_scan_pause;
      Button btnScanPause = ViewBindings.findChildViewById(rootView, id);
      if (btnScanPause == null) {
        break missingId;
      }

      id = R.id.btn_scan_resume;
      Button btnScanResume = ViewBindings.findChildViewById(rootView, id);
      if (btnScanResume == null) {
        break missingId;
      }

      id = R.id.btn_scan_stop;
      Button btnScanStop = ViewBindings.findChildViewById(rootView, id);
      if (btnScanStop == null) {
        break missingId;
      }

      id = R.id.btn_silent_scan;
      Button btnSilentScan = ViewBindings.findChildViewById(rootView, id);
      if (btnSilentScan == null) {
        break missingId;
      }

      id = R.id.btn_update_virus;
      Button btnUpdateVirus = ViewBindings.findChildViewById(rootView, id);
      if (btnUpdateVirus == null) {
        break missingId;
      }

      id = R.id.btn_uuid;
      Button btnUuid = ViewBindings.findChildViewById(rootView, id);
      if (btnUuid == null) {
        break missingId;
      }

      id = R.id.et_scan_path;
      EditText etScanPath = ViewBindings.findChildViewById(rootView, id);
      if (etScanPath == null) {
        break missingId;
      }

      ConstraintLayout main = (ConstraintLayout) rootView;

      id = R.id.rv;
      RecyclerView rv = ViewBindings.findChildViewById(rootView, id);
      if (rv == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, btnClearCache, btnConcurrentScan,
          btnCpuContinue, btnCpuPause, btnHandUpdateVirus, btnScan, btnScan24Hours, btnScanPause,
          btnScanResume, btnScanStop, btnSilentScan, btnUpdateVirus, btnUuid, etScanPath, main, rv);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
