package com.antiy.avlsdk.statistics.entity;

/**
 * 性能数据
 * 记录扫描过程中的性能指标
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class PerformanceData {
    
    /** 峰值内存使用量（MB） */
    public double peakMemoryUsage = 0.0;
    
    /** 平均内存使用量（MB） */
    public double avgMemoryUsage = 0.0;
    
    /** 平均CPU使用率（%） */
    public double avgCpuUsage = 0.0;
    
    /** 峰值CPU使用率（%） */
    public double peakCpuUsage = 0.0;
    
    /** IO操作次数 */
    public long ioOperations = 0;
    
    /** 网络请求次数（云查） */
    public int networkRequests = 0;
    
    /** 网络传输字节数 */
    public long networkBytes = 0;
    
    /** 缓存命中次数 */
    public int cacheHits = 0;
    
    /** 缓存未命中次数 */
    public int cacheMisses = 0;
    
    /** 线程池使用情况 */
    public ThreadPoolStats threadPoolStats;
    
    /** 性能采样次数 */
    private int sampleCount = 0;
    
    /** 内存使用量总和（用于计算平均值） */
    private double totalMemoryUsage = 0.0;
    
    /** CPU使用率总和（用于计算平均值） */
    private double totalCpuUsage = 0.0;
    
    public PerformanceData() {
        this.threadPoolStats = new ThreadPoolStats();
    }
    
    /**
     * 记录内存使用量
     */
    public void recordMemoryUsage(double memoryUsageMB) {
        totalMemoryUsage += memoryUsageMB;
        sampleCount++;
        
        if (memoryUsageMB > peakMemoryUsage) {
            peakMemoryUsage = memoryUsageMB;
        }
        
        avgMemoryUsage = totalMemoryUsage / sampleCount;
    }
    
    /**
     * 记录CPU使用率
     */
    public void recordCpuUsage(double cpuUsagePercent) {
        totalCpuUsage += cpuUsagePercent;
        
        if (cpuUsagePercent > peakCpuUsage) {
            peakCpuUsage = cpuUsagePercent;
        }
        
        if (sampleCount > 0) {
            avgCpuUsage = totalCpuUsage / sampleCount;
        }
    }
    
    /**
     * 增加IO操作计数
     */
    public void incrementIoOperations() {
        ioOperations++;
    }
    
    /**
     * 增加网络请求计数
     */
    public void incrementNetworkRequests(long bytes) {
        networkRequests++;
        networkBytes += bytes;
    }
    
    /**
     * 记录缓存命中
     */
    public void recordCacheHit() {
        cacheHits++;
    }
    
    /**
     * 记录缓存未命中
     */
    public void recordCacheMiss() {
        cacheMisses++;
    }
    
    /**
     * 获取缓存命中率
     */
    public double getCacheHitRate() {
        int totalCacheAccess = cacheHits + cacheMisses;
        if (totalCacheAccess == 0) {
            return 0.0;
        }
        return (double) cacheHits / totalCacheAccess * 100.0;
    }
    
    /**
     * 获取格式化的网络传输量
     */
    public String getFormattedNetworkBytes() {
        if (networkBytes < 1024) {
            return networkBytes + " B";
        } else if (networkBytes < 1024 * 1024) {
            return String.format("%.1f KB", networkBytes / 1024.0);
        } else {
            return String.format("%.1f MB", networkBytes / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 完成性能数据统计
     */
    public void finalize() {
        if (sampleCount > 0) {
            avgMemoryUsage = totalMemoryUsage / sampleCount;
            avgCpuUsage = totalCpuUsage / sampleCount;
        }
        
        threadPoolStats.finalize();
    }
    
    /**
     * 重置性能数据
     */
    public void reset() {
        peakMemoryUsage = 0.0;
        avgMemoryUsage = 0.0;
        avgCpuUsage = 0.0;
        peakCpuUsage = 0.0;
        ioOperations = 0;
        networkRequests = 0;
        networkBytes = 0;
        cacheHits = 0;
        cacheMisses = 0;
        sampleCount = 0;
        totalMemoryUsage = 0.0;
        totalCpuUsage = 0.0;
        threadPoolStats.reset();
    }
    
    @Override
    public String toString() {
        return "PerformanceData{" +
                "peakMemory=" + String.format("%.1f", peakMemoryUsage) + "MB" +
                ", avgMemory=" + String.format("%.1f", avgMemoryUsage) + "MB" +
                ", avgCpu=" + String.format("%.1f", avgCpuUsage) + "%" +
                ", ioOps=" + ioOperations +
                ", cacheHitRate=" + String.format("%.1f", getCacheHitRate()) + "%" +
                ", networkReqs=" + networkRequests +
                '}';
    }
    
    /**
     * 线程池统计数据
     */
    public static class ThreadPoolStats {
        
        /** 活跃线程数峰值 */
        public int peakActiveThreads = 0;
        
        /** 平均活跃线程数 */
        public double avgActiveThreads = 0.0;
        
        /** 任务队列最大长度 */
        public int maxQueueSize = 0;
        
        /** 已完成任务数 */
        public long completedTasks = 0;
        
        /** 任务执行总时间 */
        public long totalTaskTime = 0;
        
        /** 平均任务执行时间 */
        public double avgTaskTime = 0.0;
        
        private int threadSampleCount = 0;
        private int totalActiveThreads = 0;
        
        /**
         * 记录线程池状态
         */
        public void recordThreadPoolState(int activeThreads, int queueSize) {
            totalActiveThreads += activeThreads;
            threadSampleCount++;
            
            if (activeThreads > peakActiveThreads) {
                peakActiveThreads = activeThreads;
            }
            
            if (queueSize > maxQueueSize) {
                maxQueueSize = queueSize;
            }
            
            avgActiveThreads = (double) totalActiveThreads / threadSampleCount;
        }
        
        /**
         * 记录任务完成
         */
        public void recordTaskCompletion(long taskTime) {
            completedTasks++;
            totalTaskTime += taskTime;
            avgTaskTime = (double) totalTaskTime / completedTasks;
        }
        
        /**
         * 完成统计
         */
        public void finalize() {
            if (threadSampleCount > 0) {
                avgActiveThreads = (double) totalActiveThreads / threadSampleCount;
            }
            
            if (completedTasks > 0) {
                avgTaskTime = (double) totalTaskTime / completedTasks;
            }
        }
        
        /**
         * 重置统计数据
         */
        public void reset() {
            peakActiveThreads = 0;
            avgActiveThreads = 0.0;
            maxQueueSize = 0;
            completedTasks = 0;
            totalTaskTime = 0;
            avgTaskTime = 0.0;
            threadSampleCount = 0;
            totalActiveThreads = 0;
        }
        
        @Override
        public String toString() {
            return "ThreadPoolStats{" +
                    "peakThreads=" + peakActiveThreads +
                    ", avgThreads=" + String.format("%.1f", avgActiveThreads) +
                    ", maxQueue=" + maxQueueSize +
                    ", completedTasks=" + completedTasks +
                    ", avgTaskTime=" + String.format("%.1f", avgTaskTime) + "ms" +
                    '}';
        }
    }
}
