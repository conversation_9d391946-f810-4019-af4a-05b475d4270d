package com.antiy.avlsdk.statistics.entity;

import com.antiy.avlsdk.AVLEngine;

/**
 * 扫描统计数据
 * 记录扫描过程中的各项统计信息
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ScanStatistics {
    
    /** 总文件数 */
    public int totalFiles = 0;
    
    /** 实际扫描的文件数 */
    public int scannedFiles = 0;
    
    /** 跳过的文件数（缓存命中、文件类型不支持等） */
    public int skippedFiles = 0;
    
    /** 恶意文件数 */
    public int maliciousFiles = 0;
    
    /** 干净文件数 */
    public int cleanFiles = 0;
    
    /** 扫描出错的文件数 */
    public int errorFiles = 0;
    
    /** 缓存命中的文件数 */
    public int cacheHitFiles = 0;
    
    /** 云查文件数 */
    public int cloudScanFiles = 0;
    
    /** 本地引擎扫描文件数 */
    public int localScanFiles = 0;
    
    /** 总扫描时间（毫秒） */
    public long totalScanTime = 0;
    
    /** 平均单文件扫描时间（毫秒） */
    public double avgScanTimePerFile = 0.0;
    
    /** 最大单文件扫描时间（毫秒） */
    public long maxScanTimePerFile = 0;
    
    /** 最小单文件扫描时间（毫秒） */
    public long minScanTimePerFile = Long.MAX_VALUE;
    
    public ScanStatistics() {
    }
    
    /**
     * 增加总文件数
     */
    public void incrementTotalFiles() {
        totalFiles++;
    }
    
    /**
     * 增加扫描文件数
     */
    public void incrementScannedFiles() {
        scannedFiles++;
    }
    
    /**
     * 增加跳过文件数
     */
    public void incrementSkippedFiles() {
        skippedFiles++;
    }
    
    /**
     * 增加恶意文件数
     */
    public void incrementMaliciousFiles() {
        maliciousFiles++;
    }
    
    /**
     * 增加干净文件数
     */
    public void incrementCleanFiles() {
        cleanFiles++;
    }
    
    /**
     * 增加错误文件数
     */
    public void incrementErrorFiles() {
        errorFiles++;
    }
    
    /**
     * 增加缓存命中文件数
     */
    public void incrementCacheHitFiles() {
        cacheHitFiles++;
    }
    
    /**
     * 增加云查文件数
     */
    public void incrementCloudScanFiles() {
        cloudScanFiles++;
    }
    
    /**
     * 增加本地扫描文件数
     */
    public void incrementLocalScanFiles() {
        localScanFiles++;
    }
    
    /**
     * 记录单文件扫描时间
     */
    public void recordScanTime(long scanTime) {
        totalScanTime += scanTime;
        
        if (scanTime > maxScanTimePerFile) {
            maxScanTimePerFile = scanTime;
        }
        
        if (scanTime < minScanTimePerFile) {
            minScanTimePerFile = scanTime;
        }
        
        // 计算平均扫描时间
        if (scannedFiles > 0) {
            avgScanTimePerFile = (double) totalScanTime / scannedFiles;
        }
    }
    
    /**
     * 获取扫描成功率
     */
    public double getSuccessRate() {
        if (totalFiles == 0) {
            return 0.0;
        }
        return (double) (scannedFiles) / totalFiles * 100.0;
    }
    
    /**
     * 获取威胁检出率
     */
    public double getThreatDetectionRate() {
        if (scannedFiles == 0) {
            return 0.0;
        }
        return (double) maliciousFiles / scannedFiles * 100.0;
    }
    
    /**
     * 获取缓存命中率
     */
    public double getCacheHitRate() {
        if (totalFiles == 0) {
            return 0.0;
        }
        return (double) cacheHitFiles / totalFiles * 100.0;
    }
    
    /**
     * 获取云查使用率
     */
    public double getCloudScanRate() {
        if (scannedFiles == 0) {
            return 0.0;
        }
        return (double) cloudScanFiles / scannedFiles * 100.0;
    }
    
    /**
     * 完成统计计算
     * 在扫描结束时调用，进行最终的统计计算
     */
    public void finalize() {
        // 确保统计数据的一致性
        if (scannedFiles > 0 && minScanTimePerFile == Long.MAX_VALUE) {
            minScanTimePerFile = 0;
        }
        
        // 重新计算平均扫描时间
        if (scannedFiles > 0) {
            avgScanTimePerFile = (double) totalScanTime / scannedFiles;
        }
        
        // 验证数据一致性
        int calculatedTotal = scannedFiles + skippedFiles;
        if (calculatedTotal != totalFiles && totalFiles > 0) {
            // 记录数据不一致的情况
            AVLEngine.Logger.warn("ScanStatistics: Data inconsistency detected - " +
                "totalFiles: " + totalFiles +
                ", scannedFiles: " + scannedFiles +
                ", skippedFiles: " + skippedFiles +
                ", calculatedTotal: " + calculatedTotal);

            // 不修改totalFiles，保持原始设定值
            // totalFiles是在scanCount时设置的，应该保持不变
        }

        // 添加最终统计日志
        AVLEngine.Logger.info("ScanStatistics finalized - " +
            "totalFiles: " + totalFiles +
            ", scannedFiles: " + scannedFiles +
            ", maliciousFiles: " + maliciousFiles +
            ", cleanFiles: " + cleanFiles);
    }
    
    /**
     * 重置所有统计数据
     */
    public void reset() {
        totalFiles = 0;
        scannedFiles = 0;
        skippedFiles = 0;
        maliciousFiles = 0;
        cleanFiles = 0;
        errorFiles = 0;
        cacheHitFiles = 0;
        cloudScanFiles = 0;
        localScanFiles = 0;
        totalScanTime = 0;
        avgScanTimePerFile = 0.0;
        maxScanTimePerFile = 0;
        minScanTimePerFile = Long.MAX_VALUE;
    }
    
    @Override
    public String toString() {
        return "ScanStatistics{" +
                "totalFiles=" + totalFiles +
                ", scannedFiles=" + scannedFiles +
                ", maliciousFiles=" + maliciousFiles +
                ", cleanFiles=" + cleanFiles +
                ", avgScanTime=" + String.format("%.2f", avgScanTimePerFile) + "ms" +
                ", successRate=" + String.format("%.1f", getSuccessRate()) + "%" +
                ", threatRate=" + String.format("%.1f", getThreatDetectionRate()) + "%" +
                '}';
    }
}
