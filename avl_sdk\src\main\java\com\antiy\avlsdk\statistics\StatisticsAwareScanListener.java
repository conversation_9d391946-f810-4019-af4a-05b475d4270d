package com.antiy.avlsdk.statistics;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.entity.ScanErrorType;
import com.antiy.avlsdk.scan.FileChecker;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 统计感知的扫描监听器
 * 包装现有的ScanListener，在扫描过程中自动收集统计数据
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class StatisticsAwareScanListener implements ScanListener {
    
    private static final String TAG = "StatisticsAwareScanListener";
    
    /** 原始监听器 */
    private final ScanListener originalListener;
    
    /** 统计管理器 */
    private final ScanStatisticsManager statisticsManager;
    
    /** 统计收集器 */
    private final ScanStatisticsCollector collector;
    
    /** 文件扫描开始时间记录 */
    private final Map<String, Long> fileScanStartTimes = new HashMap<>();
    
    /** 扫描类型 */
    private String scanType = "LOCAL";
    
    /** 扫描路径 */
    private String scanPath = "";
    
    /** 是否已开始统计会话 */
    private boolean sessionStarted = false;
    
    public StatisticsAwareScanListener(ScanListener originalListener, ScanStatisticsManager statisticsManager) {
        this.originalListener = originalListener;
        this.statisticsManager = statisticsManager;
        this.collector = statisticsManager.getCollector();
    }
    
    /**
     * 设置扫描参数
     */
    public void setScanParameters(String scanType, String scanPath) {
        this.scanType = scanType != null ? scanType : "LOCAL";
        this.scanPath = scanPath != null ? scanPath : "";
    }
    
    @Override
    public void scanStart() {
        try {
            // 开始统计会话
            if (!sessionStarted) {
                statisticsManager.startScanSession(scanType, scanPath);
                sessionStarted = true;
                AVLEngine.Logger.info(TAG + ": Started statistics session for scan type: " + scanType);
            }
            
            // 调用原始监听器
            if (originalListener != null) {
                originalListener.scanStart();
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Error in scanStart: " + e.getMessage());
            // 确保原始监听器仍然被调用
            if (originalListener != null) {
                originalListener.scanStart();
            }
        }
    }
    
    @Override
    public void scanStop() {
        try {
            // 标记扫描取消
            if (sessionStarted) {
                statisticsManager.cancelScanSession();
                sessionStarted = false;
                AVLEngine.Logger.info(TAG + ": Cancelled statistics session");
            }
            
            // 调用原始监听器
            if (originalListener != null) {
                originalListener.scanStop();
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Error in scanStop: " + e.getMessage());
            // 确保原始监听器仍然被调用
            if (originalListener != null) {
                originalListener.scanStop();
            }
        }
    }
    
    @Override
    public void scanFinish() {
        try {
            // 完成统计会话
            if (sessionStarted) {
                boolean saved = statisticsManager.finishScanSession();
                sessionStarted = false;
                
                if (saved) {
                    AVLEngine.Logger.info(TAG + ": Successfully finished and saved statistics session");
                } else {
                    AVLEngine.Logger.warn(TAG + ": Failed to save statistics session");
                }
            }
            
            // 调用原始监听器
            if (originalListener != null) {
                originalListener.scanFinish();
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Error in scanFinish: " + e.getMessage());
            // 确保原始监听器仍然被调用
            if (originalListener != null) {
                originalListener.scanFinish();
            }
        }
    }
    
    @Override
    public void scanCount(int count) {
        try {
            AVLEngine.Logger.info(TAG + ": scanCount called with count: " + count);

            // 确保会话已经开始
            if (!sessionStarted) {
                AVLEngine.Logger.warn(TAG + ": scanCount called but session not started, starting session now");
                statisticsManager.startScanSession(scanType, scanPath);
                sessionStarted = true;
            }

            // 记录扫描文件总数 - 使用新的方法名避免混淆
            if (collector != null) {
                collector.setTotalFileCount(count);
                AVLEngine.Logger.info(TAG + ": Set total file count to: " + count);
            } else {
                AVLEngine.Logger.warn(TAG + ": collector is null in scanCount");
            }

            // 调用原始监听器
            if (originalListener != null) {
                originalListener.scanCount(count);
            }

        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Error in scanCount: " + e.getMessage());
            // 确保原始监听器仍然被调用
            if (originalListener != null) {
                originalListener.scanCount(count);
            }
        }
    }
    
    @Override
    public void scanFileStart(int index, String path) {
        try {
            // 记录文件扫描开始时间
            if (path != null) {
                fileScanStartTimes.put(path, System.currentTimeMillis());
                
                // 通知收集器
                if (collector != null) {
                    collector.onFileStart(path);
                }
            }
            
            // 调用原始监听器
            if (originalListener != null) {
                originalListener.scanFileStart(index, path);
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Error in scanFileStart: " + e.getMessage());
            // 确保原始监听器仍然被调用
            if (originalListener != null) {
                originalListener.scanFileStart(index, path);
            }
        }
    }
    
    @Override
    public void scanFileFinish(int index, String path, ResultScan result) {
        try {
            // 计算扫描时间
            long scanTime = 0;
            if (path != null && fileScanStartTimes.containsKey(path)) {
                Long startTime = fileScanStartTimes.remove(path);
                if (startTime != null) {
                    scanTime = System.currentTimeMillis() - startTime;
                }
            }
            
            // 确定使用的引擎
            String engine = determineEngine(path, result);
            
            // 通知收集器
            if (collector != null && result != null) {
                collector.onFileFinish(path, result, engine, scanTime);
            }
            
            // 调用原始监听器
            if (originalListener != null) {
                originalListener.scanFileFinish(index, path, result);
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Error in scanFileFinish: " + e.getMessage());
            // 确保原始监听器仍然被调用
            if (originalListener != null) {
                originalListener.scanFileFinish(index, path, result);
            }
        }
    }
    
    @Override
    public void scanError(ScanErrorType errorType) {
        try {
            // 记录扫描失败
            if (sessionStarted && errorType != null) {
                String errorMessage = getErrorMessage(errorType);
                statisticsManager.failScanSession(errorMessage);
                sessionStarted = false;
                AVLEngine.Logger.info(TAG + ": Marked statistics session as failed: " + errorMessage);
            }
            
            // 调用原始监听器
            if (originalListener != null) {
                originalListener.scanError(errorType);
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Error in scanError: " + e.getMessage());
            // 确保原始监听器仍然被调用
            if (originalListener != null) {
                originalListener.scanError(errorType);
            }
        }
    }
    
    /**
     * 记录文件跳过事件
     */
    public void onFileSkipped(String filePath, String reason) {
        try {
            if (collector != null) {
                collector.onFileSkipped(filePath, reason);
            }
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Error in onFileSkipped: " + e.getMessage());
        }
    }
    
    /**
     * 记录文件扫描错误
     */
    public void onFileError(String filePath, String errorMessage) {
        try {
            if (collector != null) {
                collector.onFileError(filePath, errorMessage);
            }
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Error in onFileError: " + e.getMessage());
        }
    }
    
    /**
     * 确定使用的扫描引擎
     */
    private String determineEngine(String filePath, ResultScan result) {
        if (result == null) {
            return "unknown";
        }
        
        // 如果是云查结果
        if (result.isCloudScan) {
            return "cloud";
        }
        
        // 根据文件类型判断引擎
        if (filePath != null) {
            try {
                File file = new File(filePath);
                if (FileChecker.getInstance().isApkOrJarOrDex(file)) {
                    return "mobile";
                } else {
                    return "pc";
                }
            } catch (Exception e) {
                AVLEngine.Logger.warn(TAG + ": Failed to determine engine for file: " + filePath);
            }
        }
        
        return "unknown";
    }
    
    /**
     * 获取错误消息
     */
    private String getErrorMessage(ScanErrorType errorType) {
        if (errorType == null) {
            return "未知错误";
        }
        
        // 这里可以根据ScanErrorType的具体实现来返回相应的错误消息
        return "扫描错误: " + errorType.toString();
    }
    
    /**
     * 获取原始监听器
     */
    public ScanListener getOriginalListener() {
        return originalListener;
    }
    
    /**
     * 检查统计会话是否已开始
     */
    public boolean isSessionStarted() {
        return sessionStarted;
    }
    
    /**
     * 强制结束统计会话（用于异常情况）
     */
    public void forceEndSession() {
        if (sessionStarted) {
            try {
                statisticsManager.cancelScanSession();
                sessionStarted = false;
                AVLEngine.Logger.info(TAG + ": Force ended statistics session");
            } catch (Exception e) {
                AVLEngine.Logger.error(TAG + ": Error in forceEndSession: " + e.getMessage());
            }
        }
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        fileScanStartTimes.clear();
        forceEndSession();
    }
}
