package com.antiy.demo

import android.app.Application
import com.antiy.avlsdk.AVLEngine
import com.antiy.demo.statistics.ScanStatisticsIntegration
import com.antiy.demo.statistics.StatisticsConfig
import java.util.UUID


/**
2 * Copyright (C), 2020-2024
3 * FileName: App
4 * Author: wangbiao
5 * Date: 2024/9/2 15:06
6 * Description:
10 */
class App : Application() {
    // 保存UUID供初始化时使用
    private val uuid = UUID.randomUUID().toString()

    override fun onCreate() {
        super.onCreate()
        // 在这里不再初始化AVLEngine，而是在获取权限后调用
    }

    /**
     * 初始化 AVLEngine
     * 在获取到文件权限后调用此方法
     *
     * @return 返回初始化后的UUID
     */
    fun initializeAVLEngine(): String {
        // 1. 先初始化SDK
        val result = AVLEngine.init(this, uuid, ALog(this), NetworkManager())

        // 2. 如果SDK初始化成功，立即配置统计功能
        if (result.isSuccess) {
            initializeStatistics()
        }

        return uuid
    }

    /**
     * 初始化统计功能
     */
    private fun initializeStatistics() {
        try {
            val config = StatisticsConfig.getDefault(this)
            val success = ScanStatisticsIntegration.enableStatistics(this, config)

            if (success) {
                android.util.Log.i("App", "统计功能初始化成功")
            } else {
                android.util.Log.w("App", "统计功能初始化失败")
            }
        } catch (e: Exception) {
            android.util.Log.e("App", "统计功能初始化异常", e)
        }
    }

    /**
     * 检查AVLEngine是否已初始化
     *
     * @return 如果已初始化则返回true，否则返回false
     */
    fun isAVLEngineInitialized(): Boolean {
        return AVLEngine.getInstance().initResult?.isSuccess == true
    }

    /**
     * 检查统计功能是否已启用
     *
     * @return 如果统计功能已启用则返回true，否则返回false
     */
    fun isStatisticsEnabled(): Boolean {
        return ScanStatisticsIntegration.isEnabled()
    }

    /**
     * 获取统计功能状态信息
     *
     * @return 状态信息字符串
     */
    fun getStatisticsStatusInfo(): String {
        return ScanStatisticsIntegration.getStatusInfo()
    }
}
