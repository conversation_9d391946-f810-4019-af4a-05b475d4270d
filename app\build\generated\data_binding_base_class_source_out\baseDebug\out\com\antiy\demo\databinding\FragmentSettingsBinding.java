// Generated by view binder compiler. Do not edit!
package com.antiy.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.Switch;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.antiy.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final LinearLayout clearCacheLl;

  @NonNull
  public final LinearLayout exportStatisticsLl;

  @NonNull
  public final LinearLayout layoutAbout;

  @NonNull
  public final LinearLayout layoutAntivirusEngine;

  @NonNull
  public final LinearLayout layoutCloudScan;

  @NonNull
  public final LinearLayout layoutHelp;

  @NonNull
  public final LinearLayout layoutLanguage;

  @NonNull
  public final LinearLayout layoutLogout;

  @NonNull
  public final LinearLayout layoutNotification;

  @NonNull
  public final LinearLayout layoutPerformance;

  @NonNull
  public final LinearLayout layoutUserProfile;

  @NonNull
  public final Switch switchDarkMode;

  @NonNull
  public final LinearLayout viewStatisticsLl;

  private FragmentSettingsBinding(@NonNull ScrollView rootView, @NonNull LinearLayout clearCacheLl,
      @NonNull LinearLayout exportStatisticsLl, @NonNull LinearLayout layoutAbout,
      @NonNull LinearLayout layoutAntivirusEngine, @NonNull LinearLayout layoutCloudScan,
      @NonNull LinearLayout layoutHelp, @NonNull LinearLayout layoutLanguage,
      @NonNull LinearLayout layoutLogout, @NonNull LinearLayout layoutNotification,
      @NonNull LinearLayout layoutPerformance, @NonNull LinearLayout layoutUserProfile,
      @NonNull Switch switchDarkMode, @NonNull LinearLayout viewStatisticsLl) {
    this.rootView = rootView;
    this.clearCacheLl = clearCacheLl;
    this.exportStatisticsLl = exportStatisticsLl;
    this.layoutAbout = layoutAbout;
    this.layoutAntivirusEngine = layoutAntivirusEngine;
    this.layoutCloudScan = layoutCloudScan;
    this.layoutHelp = layoutHelp;
    this.layoutLanguage = layoutLanguage;
    this.layoutLogout = layoutLogout;
    this.layoutNotification = layoutNotification;
    this.layoutPerformance = layoutPerformance;
    this.layoutUserProfile = layoutUserProfile;
    this.switchDarkMode = switchDarkMode;
    this.viewStatisticsLl = viewStatisticsLl;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.clear_cache_ll;
      LinearLayout clearCacheLl = ViewBindings.findChildViewById(rootView, id);
      if (clearCacheLl == null) {
        break missingId;
      }

      id = R.id.export_statistics_ll;
      LinearLayout exportStatisticsLl = ViewBindings.findChildViewById(rootView, id);
      if (exportStatisticsLl == null) {
        break missingId;
      }

      id = R.id.layoutAbout;
      LinearLayout layoutAbout = ViewBindings.findChildViewById(rootView, id);
      if (layoutAbout == null) {
        break missingId;
      }

      id = R.id.layoutAntivirusEngine;
      LinearLayout layoutAntivirusEngine = ViewBindings.findChildViewById(rootView, id);
      if (layoutAntivirusEngine == null) {
        break missingId;
      }

      id = R.id.layoutCloudScan;
      LinearLayout layoutCloudScan = ViewBindings.findChildViewById(rootView, id);
      if (layoutCloudScan == null) {
        break missingId;
      }

      id = R.id.layoutHelp;
      LinearLayout layoutHelp = ViewBindings.findChildViewById(rootView, id);
      if (layoutHelp == null) {
        break missingId;
      }

      id = R.id.layoutLanguage;
      LinearLayout layoutLanguage = ViewBindings.findChildViewById(rootView, id);
      if (layoutLanguage == null) {
        break missingId;
      }

      id = R.id.layoutLogout;
      LinearLayout layoutLogout = ViewBindings.findChildViewById(rootView, id);
      if (layoutLogout == null) {
        break missingId;
      }

      id = R.id.layoutNotification;
      LinearLayout layoutNotification = ViewBindings.findChildViewById(rootView, id);
      if (layoutNotification == null) {
        break missingId;
      }

      id = R.id.layoutPerformance;
      LinearLayout layoutPerformance = ViewBindings.findChildViewById(rootView, id);
      if (layoutPerformance == null) {
        break missingId;
      }

      id = R.id.layoutUserProfile;
      LinearLayout layoutUserProfile = ViewBindings.findChildViewById(rootView, id);
      if (layoutUserProfile == null) {
        break missingId;
      }

      id = R.id.switchDarkMode;
      Switch switchDarkMode = ViewBindings.findChildViewById(rootView, id);
      if (switchDarkMode == null) {
        break missingId;
      }

      id = R.id.view_statistics_ll;
      LinearLayout viewStatisticsLl = ViewBindings.findChildViewById(rootView, id);
      if (viewStatisticsLl == null) {
        break missingId;
      }

      return new FragmentSettingsBinding((ScrollView) rootView, clearCacheLl, exportStatisticsLl,
          layoutAbout, layoutAntivirusEngine, layoutCloudScan, layoutHelp, layoutLanguage,
          layoutLogout, layoutNotification, layoutPerformance, layoutUserProfile, switchDarkMode,
          viewStatisticsLl);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
