package com.antiy.avlsdk.statistics.entity;

/**
 * 引擎统计数据
 * 记录不同扫描引擎的统计信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class EngineStatistics {
    
    /** 移动引擎统计 */
    public EngineStats mobileEngine;
    
    /** PC引擎统计 */
    public EngineStats pcEngine;
    
    /** 云查引擎统计 */
    public EngineStats cloudEngine;
    
    public EngineStatistics() {
        this.mobileEngine = new EngineStats("mobile");
        this.pcEngine = new EngineStats("pc");
        this.cloudEngine = new EngineStats("cloud");
    }
    
    /**
     * 记录移动引擎扫描结果
     */
    public void recordMobileEngineScan(boolean isMalicious, long scanTime) {
        mobileEngine.recordScan(isMalicious, scanTime);
    }
    
    /**
     * 记录PC引擎扫描结果
     */
    public void recordPcEngineScan(boolean isMalicious, long scanTime) {
        pcEngine.recordScan(isMalicious, scanTime);
    }
    
    /**
     * 记录云查引擎扫描结果
     */
    public void recordCloudEngineScan(boolean isMalicious, long scanTime) {
        cloudEngine.recordScan(isMalicious, scanTime);
    }
    
    /**
     * 获取总扫描文件数
     */
    public int getTotalFilesScanned() {
        return mobileEngine.filesScanned + pcEngine.filesScanned + cloudEngine.filesScanned;
    }
    
    /**
     * 获取总威胁数
     */
    public int getTotalThreatsFound() {
        return mobileEngine.threatsFound + pcEngine.threatsFound + cloudEngine.threatsFound;
    }
    
    /**
     * 获取总扫描时间
     */
    public long getTotalScanTime() {
        return mobileEngine.totalScanTime + pcEngine.totalScanTime + cloudEngine.totalScanTime;
    }
    
    /**
     * 获取平均扫描时间
     */
    public double getAverageScanTime() {
        int totalFiles = getTotalFilesScanned();
        if (totalFiles == 0) {
            return 0.0;
        }
        return (double) getTotalScanTime() / totalFiles;
    }
    
    /**
     * 完成统计计算
     */
    public void finalize() {
        mobileEngine.finalize();
        pcEngine.finalize();
        cloudEngine.finalize();
    }
    
    @Override
    public String toString() {
        return "EngineStatistics{" +
                "mobile=" + mobileEngine +
                ", pc=" + pcEngine +
                ", cloud=" + cloudEngine +
                ", totalFiles=" + getTotalFilesScanned() +
                ", totalThreats=" + getTotalThreatsFound() +
                '}';
    }
    
    /**
     * 单个引擎的统计数据
     */
    public static class EngineStats {
        
        /** 引擎名称 */
        public String engineName;
        
        /** 扫描文件数 */
        public int filesScanned = 0;
        
        /** 发现威胁数 */
        public int threatsFound = 0;
        
        /** 总扫描时间（毫秒） */
        public long totalScanTime = 0;
        
        /** 平均扫描时间（毫秒） */
        public double avgScanTime = 0.0;
        
        /** 最大扫描时间（毫秒） */
        public long maxScanTime = 0;
        
        /** 最小扫描时间（毫秒） */
        public long minScanTime = Long.MAX_VALUE;
        
        /** 威胁检出率 */
        public double threatDetectionRate = 0.0;
        
        public EngineStats(String engineName) {
            this.engineName = engineName;
        }
        
        /**
         * 记录一次扫描结果
         */
        public void recordScan(boolean isMalicious, long scanTime) {
            filesScanned++;
            totalScanTime += scanTime;
            
            if (isMalicious) {
                threatsFound++;
            }
            
            // 更新最大最小扫描时间
            if (scanTime > maxScanTime) {
                maxScanTime = scanTime;
            }
            
            if (scanTime < minScanTime) {
                minScanTime = scanTime;
            }
            
            // 计算平均扫描时间
            avgScanTime = (double) totalScanTime / filesScanned;
            
            // 计算威胁检出率
            threatDetectionRate = (double) threatsFound / filesScanned * 100.0;
        }
        
        /**
         * 完成统计计算
         */
        public void finalize() {
            if (filesScanned > 0) {
                avgScanTime = (double) totalScanTime / filesScanned;
                threatDetectionRate = (double) threatsFound / filesScanned * 100.0;
                
                // 如果没有扫描任何文件，重置最小扫描时间
                if (minScanTime == Long.MAX_VALUE) {
                    minScanTime = 0;
                }
            } else {
                avgScanTime = 0.0;
                threatDetectionRate = 0.0;
                minScanTime = 0;
                maxScanTime = 0;
            }
        }
        
        /**
         * 获取引擎效率（文件数/秒）
         */
        public double getEfficiency() {
            if (totalScanTime == 0) {
                return 0.0;
            }
            return (double) filesScanned / (totalScanTime / 1000.0);
        }
        
        /**
         * 重置统计数据
         */
        public void reset() {
            filesScanned = 0;
            threatsFound = 0;
            totalScanTime = 0;
            avgScanTime = 0.0;
            maxScanTime = 0;
            minScanTime = Long.MAX_VALUE;
            threatDetectionRate = 0.0;
        }
        
        @Override
        public String toString() {
            return "EngineStats{" +
                    "engine='" + engineName + '\'' +
                    ", files=" + filesScanned +
                    ", threats=" + threatsFound +
                    ", avgTime=" + String.format("%.1f", avgScanTime) + "ms" +
                    ", detectionRate=" + String.format("%.1f", threatDetectionRate) + "%" +
                    '}';
        }
    }
}
