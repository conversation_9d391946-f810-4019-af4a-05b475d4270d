# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-ARM64_V8A-model 40ms
    [gap of 50ms]
  create-initial-cxx-model completed in 98ms
create_cxx_tasks completed in 102ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 61ms
create_cxx_tasks completed in 66ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 58ms]
    create-variant-model 10ms
    create-X86_64-model 10ms
    create-module-model 10ms
    [gap of 13ms]
    create-variant-model 19ms
    [gap of 12ms]
  create-initial-cxx-model completed in 141ms
create_cxx_tasks completed in 144ms

