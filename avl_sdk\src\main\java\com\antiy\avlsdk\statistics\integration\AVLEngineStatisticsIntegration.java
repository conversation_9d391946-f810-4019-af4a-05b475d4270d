package com.antiy.avlsdk.statistics.integration;

import android.content.Context;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.statistics.ScanStatisticsFactory;
import com.antiy.avlsdk.statistics.ScanStatisticsManager;

/**
 * AVL 引擎统计功能集成类
 * 提供便捷的方法将统计功能集成到现有的 AVLEngine 使用中
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class AVLEngineStatisticsIntegration {
    
    private static final String TAG = "AVLEngineStatisticsIntegration";
    
    /** 是否已初始化统计功能 */
    private static boolean statisticsInitialized = false;
    
    /**
     * 初始化 AVL 引擎统计功能
     * 建议在应用启动时调用
     * 
     * @param context 应用上下文
     * @return 是否初始化成功
     */
    public static boolean initializeStatistics(Context context) {
        if (statisticsInitialized) {
            AVLEngine.Logger.info(TAG + ": Statistics already initialized");
            return true;
        }
        
        boolean success = ScanStatisticsFactory.initialize(context);
        if (success) {
            statisticsInitialized = true;
            AVLEngine.Logger.info(TAG + ": Statistics initialized successfully");
            
            // 输出统计功能状态
            ScanStatisticsFactory.logStatisticsStatus();
        } else {
            AVLEngine.Logger.error(TAG + ": Failed to initialize statistics");
        }
        
        return success;
    }
    
    /**
     * 创建带统计功能的扫描监听器
     * 
     * @param originalListener 原始监听器
     * @param scanPath 扫描路径
     * @return 包装后的监听器
     */
    public static ScanListener createStatisticsEnabledListener(ScanListener originalListener, String scanPath) {
        if (!statisticsInitialized) {
            AVLEngine.Logger.warn(TAG + ": Statistics not initialized, returning original listener");
            return originalListener;
        }
        
        // 根据扫描路径和网络状态确定扫描类型
        String scanType = determineScanType(scanPath);
        
        return ScanStatisticsFactory.createStatisticsAwareListener(originalListener, scanType, scanPath);
    }
    
    /**
     * 扫描目录（带统计功能）
     *
     * @param scanPath 扫描路径
     * @param originalListener 原始监听器
     */
    public static void scanDirWithStatistics(String scanPath, ScanListener originalListener) {
        ScanListener statisticsListener = createStatisticsEnabledListener(originalListener, scanPath);

        // 预先计算文件数量（作为备用，如果AVLEngine没有调用scanCount的话）
        // 注意：不在这里调用scanStart和scanCount，让AVLEngine自己调用
        // 这里只是记录预期的文件数量用于调试
        try {
            java.io.File dir = new java.io.File(scanPath);
            if (dir.exists() && dir.isDirectory()) {
                int fileCount = countFilesInDirectory(dir);
                AVLEngine.Logger.info(TAG + ": Expected file count for " + scanPath + ": " + fileCount);
            }
        } catch (Exception e) {
            AVLEngine.Logger.warn(TAG + ": Failed to calculate expected file count: " + e.getMessage());
        }

        AVLEngine.getInstance().scanDir(scanPath, statisticsListener);
    }

    /**
     * 递归计算目录中的文件数量
     */
    private static int countFilesInDirectory(java.io.File dir) {
        int count = 0;
        try {
            java.io.File[] files = dir.listFiles();
            if (files != null) {
                for (java.io.File file : files) {
                    if (file.isFile()) {
                        count++;
                    } else if (file.isDirectory()) {
                        count += countFilesInDirectory(file);
                    }
                }
            }
        } catch (Exception e) {
            AVLEngine.Logger.warn(TAG + ": Error counting files in " + dir.getPath() + ": " + e.getMessage());
        }
        return count;
    }
    
    /**
     * 扫描文件（带统计功能）
     * 
     * @param filePath 文件路径
     * @param originalListener 原始监听器
     */
    public static void scanFileWithStatistics(String filePath, ScanListener originalListener) {
        ScanListener statisticsListener = createStatisticsEnabledListener(originalListener, filePath);
//        AVLEngine.getInstance().scanFile(filePath, statisticsListener);
    }
    
    /**
     * 获取最近扫描摘要
     * 
     * @param limit 记录数量限制
     * @return 摘要字符串
     */
    public static String getRecentScansSummary(int limit) {
        if (!statisticsInitialized) {
            return "统计功能未初始化";
        }
        
        return ScanStatisticsFactory.getRecentScansSummary(limit);
    }
    
    /**
     * 获取今日扫描统计
     * 
     * @return 今日统计摘要
     */
    public static String getTodayStatistics() {
        if (!statisticsInitialized) {
            return "统计功能未初始化";
        }
        
        return ScanStatisticsFactory.getTodayStatisticsSummary();
    }
    
    /**
     * 导出扫描统计报告
     * 
     * @param outputPath 输出路径
     * @param days 统计天数
     * @return 导出结果
     */
    public static String exportStatisticsReport(String outputPath, int days) {
        if (!statisticsInitialized) {
            return "统计功能未初始化";
        }
        
        return ScanStatisticsFactory.exportStatisticsReport(outputPath, days);
    }
    
    /**
     * 清理过期统计记录
     * 
     * @return 清理结果
     */
    public static String cleanupExpiredRecords() {
        if (!statisticsInitialized) {
            return "统计功能未初始化";
        }
        
        return ScanStatisticsFactory.cleanupExpiredRecords();
    }
    
    /**
     * 获取统计管理器
     * 
     * @return 统计管理器实例，如果未初始化则返回 null
     */
    public static ScanStatisticsManager getStatisticsManager() {
        if (!statisticsInitialized) {
            return null;
        }
        
        return ScanStatisticsFactory.getStatisticsManager();
    }
    
    /**
     * 检查统计功能是否已初始化
     * 
     * @return 是否已初始化
     */
    public static boolean isStatisticsInitialized() {
        return statisticsInitialized;
    }
    
    /**
     * 输出统计功能状态到日志
     */
    public static void logStatisticsStatus() {
        if (statisticsInitialized) {
            ScanStatisticsFactory.logStatisticsStatus();
        } else {
            AVLEngine.Logger.info(TAG + ": Statistics not initialized");
        }
    }
    
    /**
     * 根据扫描路径确定扫描类型
     * 
     * @param scanPath 扫描路径
     * @return 扫描类型
     */
    private static String determineScanType(String scanPath) {
        // 这里可以根据实际需求来判断扫描类型
        // 例如：根据网络状态、文件数量、扫描路径等因素
        
        if (scanPath == null || scanPath.isEmpty()) {
            return "LOCAL";
        }
        
        // 简单的判断逻辑，可以根据实际需求调整
        if (scanPath.contains("/sdcard/") || scanPath.contains("/storage/")) {
            return "LOCAL";
        } else if (scanPath.contains("/data/")) {
            return "LOCAL";
        } else {
            return "LOCAL";
        }
    }
    
    /**
     * 使用示例方法
     * 展示如何在现有代码中集成统计功能
     */
    public static void showUsageExample(Context context) {
        AVLEngine.Logger.info(TAG + ": === 统计功能集成示例 ===");
        
        // 1. 初始化统计功能
        boolean initialized = initializeStatistics(context);
        if (!initialized) {
            AVLEngine.Logger.error(TAG + ": 统计功能初始化失败，示例终止");
            return;
        }
        
        // 2. 创建原始扫描监听器
        ScanListener originalListener = new ScanListener() {
            @Override
            public void scanStart() {
                AVLEngine.Logger.info(TAG + ": 扫描开始");
            }
            
            @Override
            public void scanStop() {
                AVLEngine.Logger.info(TAG + ": 扫描停止");
            }
            
            @Override
            public void scanFinish() {
                AVLEngine.Logger.info(TAG + ": 扫描完成");
                
                // 扫描完成后，输出统计信息
                AVLEngine.Logger.info(TAG + ": " + getRecentScansSummary(1));
                AVLEngine.Logger.info(TAG + ": " + getTodayStatistics());
            }
            
            @Override
            public void scanCount(int count) {
                AVLEngine.Logger.info(TAG + ": 需要扫描 " + count + " 个文件");
            }
            
            @Override
            public void scanFileStart(int index, String path) {
                // 可以选择性地输出，避免日志过多
            }
            
            @Override
            public void scanFileFinish(int index, String path, com.antiy.avlsdk.entity.ResultScan result) {
                // 可以选择性地输出，避免日志过多
            }
            
            @Override
            public void scanError(com.antiy.avlsdk.entity.ScanErrorType errorType) {
                AVLEngine.Logger.error(TAG + ": 扫描错误：" + errorType);
            }
        };
        
        // 3. 使用带统计功能的扫描方法
        String testPath = "/sdcard/test";
        AVLEngine.Logger.info(TAG + ": 开始扫描路径：" + testPath);
        
        // 方法一：直接调用集成方法
        // scanDirWithStatistics(testPath, originalListener);
        
        // 方法二：手动创建统计监听器
        ScanListener statisticsListener = createStatisticsEnabledListener(originalListener, testPath);
        // AVLEngine.getInstance().scanDir(testPath, statisticsListener);
        
        AVLEngine.Logger.info(TAG + ": 统计功能集成示例完成");
        AVLEngine.Logger.info(TAG + ": 注意：实际扫描代码已注释，请根据需要取消注释");
    }
}
