package com.antiy.avlsdk.statistics.example;

import android.content.Context;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.entity.ScanErrorType;
import com.antiy.avlsdk.statistics.ScanStatisticsFactory;
import com.antiy.avlsdk.statistics.ScanStatisticsManager;
import com.antiy.avlsdk.statistics.entity.ScanSessionRecord;

import java.util.List;

/**
 * 扫描统计功能使用示例
 * 展示如何在现有代码中集成和使用扫描统计功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class StatisticsUsageExample {
    
    private static final String TAG = "StatisticsUsageExample";
    
    /**
     * 示例1：基本使用方式
     * 在应用初始化时启用统计功能
     */
    public static void example1_BasicUsage(Context context) {
        AVLEngine.Logger.info(TAG + ": === 示例1：基本使用方式 ===");
        
        // 1. 初始化统计功能
        boolean initialized = ScanStatisticsFactory.initialize(context);
        if (!initialized) {
            AVLEngine.Logger.error(TAG + ": 统计功能初始化失败");
            return;
        }
        
        // 2. 创建原始扫描监听器
        ScanListener originalListener = new ScanListener() {
            @Override
            public void scanStart() {
                AVLEngine.Logger.info(TAG + ": 扫描开始");
            }
            
            @Override
            public void scanStop() {
                AVLEngine.Logger.info(TAG + ": 扫描停止");
            }
            
            @Override
            public void scanFinish() {
                AVLEngine.Logger.info(TAG + ": 扫描完成");
            }
            
            @Override
            public void scanCount(int count) {
                AVLEngine.Logger.info(TAG + ": 需要扫描 " + count + " 个文件");
            }
            
            @Override
            public void scanFileStart(int index, String path) {
                AVLEngine.Logger.info(TAG + ": 开始扫描第 " + index + " 个文件: " + path);
            }
            
            @Override
            public void scanFileFinish(int index, String path, ResultScan result) {
                AVLEngine.Logger.info(TAG + ": 完成扫描第 " + index + " 个文件: " + path + 
                        ", 结果: " + (result.isMalicious ? "威胁" : "安全"));
            }
            
            @Override
            public void scanError(ScanErrorType errorType) {
                AVLEngine.Logger.error(TAG + ": 扫描错误: " + errorType);
            }
        };
        
        // 3. 创建统计感知的监听器
        ScanListener statisticsAwareListener = ScanStatisticsFactory.createStatisticsAwareListener(
                originalListener, "LOCAL", "/sdcard/test");
        
        // 4. 使用统计感知的监听器进行扫描
        // AVLEngine.getInstance().scanDir("/sdcard/test", statisticsAwareListener);
        
        AVLEngine.Logger.info(TAG + ": 基本使用示例完成");
    }
    
    /**
     * 示例2：查询统计数据
     */
    public static void example2_QueryStatistics() {
        AVLEngine.Logger.info(TAG + ": === 示例2：查询统计数据 ===");
        
        // 检查统计功能是否已初始化
        if (!ScanStatisticsFactory.isInitialized()) {
            AVLEngine.Logger.warn(TAG + ": 统计功能未初始化");
            return;
        }
        
        // 获取统计管理器
        ScanStatisticsManager manager = ScanStatisticsFactory.getStatisticsManager();
        if (manager == null) {
            AVLEngine.Logger.error(TAG + ": 无法获取统计管理器");
            return;
        }
        
        // 1. 获取最近的扫描记录
        AVLEngine.Logger.info(TAG + ": " + ScanStatisticsFactory.getRecentScansSummary(5));
        
        // 2. 获取今日统计
        AVLEngine.Logger.info(TAG + ": " + ScanStatisticsFactory.getTodayStatisticsSummary());
        
        // 3. 获取存储使用情况
        AVLEngine.Logger.info(TAG + ": " + ScanStatisticsFactory.getStorageUsageSummary());
        
        // 4. 获取指定扫描记录的详细信息
        List<ScanSessionRecord> recentScans = manager.getRecentScans(1);
        if (recentScans != null && !recentScans.isEmpty()) {
            ScanSessionRecord latestScan = recentScans.get(0);
            AVLEngine.Logger.info(TAG + ": 最新扫描详情: " + latestScan.toString());
            
            // 输出详细统计到日志
            manager.logStatisticsSummary(latestScan.scanId);
        }
        
        AVLEngine.Logger.info(TAG + ": 查询统计数据示例完成");
    }
    
    /**
     * 示例3：导出统计报告
     */
    public static void example3_ExportReports(Context context) {
        AVLEngine.Logger.info(TAG + ": === 示例3：导出统计报告 ===");
        
        if (!ScanStatisticsFactory.isInitialized()) {
            AVLEngine.Logger.warn(TAG + ": 统计功能未初始化");
            return;
        }
        
        ScanStatisticsManager manager = ScanStatisticsFactory.getStatisticsManager();
        if (manager == null) {
            return;
        }
        
        try {
            // 1. 导出JSON格式报告（最近7天）
            String jsonPath = context.getExternalFilesDir(null) + "/scan_report.json";
            String jsonResult = ScanStatisticsFactory.exportStatisticsReport(jsonPath, 7);
            AVLEngine.Logger.info(TAG + ": JSON报告: " + jsonResult);
            
            // 2. 导出CSV格式报告（最近30天）
            String csvPath = context.getExternalFilesDir(null) + "/scan_report.csv";
            long endTime = System.currentTimeMillis();
            long startTime = endTime - (30 * 24 * 60 * 60 * 1000L);
            
            String csvResult = manager.exportToCsv(startTime, endTime, csvPath, "UTF-8");
            if (csvResult != null) {
                AVLEngine.Logger.info(TAG + ": CSV报告已导出到: " + csvResult);
            } else {
                AVLEngine.Logger.warn(TAG + ": CSV报告导出失败");
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": 导出报告时发生异常: " + e.getMessage());
        }
        
        AVLEngine.Logger.info(TAG + ": 导出统计报告示例完成");
    }
    
    /**
     * 示例4：维护和清理
     */
    public static void example4_MaintenanceAndCleanup() {
        AVLEngine.Logger.info(TAG + ": === 示例4：维护和清理 ===");
        
        if (!ScanStatisticsFactory.isInitialized()) {
            AVLEngine.Logger.warn(TAG + ": 统计功能未初始化");
            return;
        }
        
        // 1. 输出统计功能状态
        ScanStatisticsFactory.logStatisticsStatus();
        
        // 2. 清理过期记录
        String cleanupResult = ScanStatisticsFactory.cleanupExpiredRecords();
        AVLEngine.Logger.info(TAG + ": " + cleanupResult);
        
        // 3. 再次检查存储使用情况
        AVLEngine.Logger.info(TAG + ": 清理后的存储情况:");
        AVLEngine.Logger.info(TAG + ": " + ScanStatisticsFactory.getStorageUsageSummary());
        
        AVLEngine.Logger.info(TAG + ": 维护和清理示例完成");
    }
    
    /**
     * 示例5：高级用法 - 直接使用统计管理器API
     */
    public static void example5_AdvancedUsage() {
        AVLEngine.Logger.info(TAG + ": === 示例5：高级用法 ===");
        
        if (!ScanStatisticsFactory.isInitialized()) {
            AVLEngine.Logger.warn(TAG + ": 统计功能未初始化");
            return;
        }
        
        ScanStatisticsManager manager = ScanStatisticsFactory.getStatisticsManager();
        if (manager == null) {
            return;
        }
        
        try {
            // 1. 获取指定时间范围的扫描记录
            long endTime = System.currentTimeMillis();
            long startTime = endTime - (7 * 24 * 60 * 60 * 1000L); // 最近7天

            List<ScanSessionRecord> records = manager.getScansByTimeRange(startTime, endTime);
            AVLEngine.Logger.info(TAG + ": 最近7天共有 " + (records != null ? records.size() : 0) + " 次扫描");

            // 2. 获取指定日期的统计
            String today = new java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
                    .format(new java.util.Date());
            ScanStatisticsManager.DailyStatistics dailyStats = manager.getDailyStatistics(today);
            if (dailyStats != null) {
                AVLEngine.Logger.info(TAG + ": 今日统计: " + dailyStats.toString());
            }

            // 3. 获取威胁统计（如果有实现）
            List<ScanStatisticsManager.ThreatStatistics> threatStats = manager.getThreatStatistics(startTime, endTime);
            if (threatStats != null && !threatStats.isEmpty()) {
                AVLEngine.Logger.info(TAG + ": 威胁统计: " + threatStats.size() + " 种威胁类型");
                for (ScanStatisticsManager.ThreatStatistics threat : threatStats) {
                    AVLEngine.Logger.info(TAG + ": - " + threat.toString());
                }
            } else {
                AVLEngine.Logger.info(TAG + ": 最近7天未发现威胁");
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": 高级用法示例发生异常: " + e.getMessage());
        }
        
        AVLEngine.Logger.info(TAG + ": 高级用法示例完成");
    }
    
    /**
     * 运行所有示例
     */
    public static void runAllExamples(Context context) {
        AVLEngine.Logger.info(TAG + ": ========== 开始运行扫描统计功能示例 ==========");
        
        example1_BasicUsage(context);
        example2_QueryStatistics();
        example3_ExportReports(context);
        example4_MaintenanceAndCleanup();
        example5_AdvancedUsage();
        
        AVLEngine.Logger.info(TAG + ": ========== 所有示例运行完成 ==========");
    }
}
