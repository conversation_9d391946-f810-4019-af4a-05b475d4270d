// Generated by view binder compiler. Do not edit!
package com.antiy.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.ScrollView;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.antiy.demo.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentScanBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton btnSelectFileScan;

  @NonNull
  public final Button buttonStartScan;

  @NonNull
  public final CardView cardCustomScan;

  @NonNull
  public final CardView cardFullScan;

  @NonNull
  public final CardView cardQuickScan;

  @NonNull
  public final CardView cardScheduledScan;

  @NonNull
  public final CheckBox checkBoxBootScan;

  @NonNull
  public final CheckBox checkBoxCloudScan;

  @NonNull
  public final Spinner spinnerCpuLimit;

  @NonNull
  public final TextView textFileScanResult;

  @NonNull
  public final TextView textSelectedFile;

  private FragmentScanBinding(@NonNull ScrollView rootView,
      @NonNull MaterialButton btnSelectFileScan, @NonNull Button buttonStartScan,
      @NonNull CardView cardCustomScan, @NonNull CardView cardFullScan,
      @NonNull CardView cardQuickScan, @NonNull CardView cardScheduledScan,
      @NonNull CheckBox checkBoxBootScan, @NonNull CheckBox checkBoxCloudScan,
      @NonNull Spinner spinnerCpuLimit, @NonNull TextView textFileScanResult,
      @NonNull TextView textSelectedFile) {
    this.rootView = rootView;
    this.btnSelectFileScan = btnSelectFileScan;
    this.buttonStartScan = buttonStartScan;
    this.cardCustomScan = cardCustomScan;
    this.cardFullScan = cardFullScan;
    this.cardQuickScan = cardQuickScan;
    this.cardScheduledScan = cardScheduledScan;
    this.checkBoxBootScan = checkBoxBootScan;
    this.checkBoxCloudScan = checkBoxCloudScan;
    this.spinnerCpuLimit = spinnerCpuLimit;
    this.textFileScanResult = textFileScanResult;
    this.textSelectedFile = textSelectedFile;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentScanBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentScanBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_scan, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentScanBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnSelectFileScan;
      MaterialButton btnSelectFileScan = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectFileScan == null) {
        break missingId;
      }

      id = R.id.buttonStartScan;
      Button buttonStartScan = ViewBindings.findChildViewById(rootView, id);
      if (buttonStartScan == null) {
        break missingId;
      }

      id = R.id.cardCustomScan;
      CardView cardCustomScan = ViewBindings.findChildViewById(rootView, id);
      if (cardCustomScan == null) {
        break missingId;
      }

      id = R.id.cardFullScan;
      CardView cardFullScan = ViewBindings.findChildViewById(rootView, id);
      if (cardFullScan == null) {
        break missingId;
      }

      id = R.id.cardQuickScan;
      CardView cardQuickScan = ViewBindings.findChildViewById(rootView, id);
      if (cardQuickScan == null) {
        break missingId;
      }

      id = R.id.cardScheduledScan;
      CardView cardScheduledScan = ViewBindings.findChildViewById(rootView, id);
      if (cardScheduledScan == null) {
        break missingId;
      }

      id = R.id.checkBoxBootScan;
      CheckBox checkBoxBootScan = ViewBindings.findChildViewById(rootView, id);
      if (checkBoxBootScan == null) {
        break missingId;
      }

      id = R.id.checkBoxCloudScan;
      CheckBox checkBoxCloudScan = ViewBindings.findChildViewById(rootView, id);
      if (checkBoxCloudScan == null) {
        break missingId;
      }

      id = R.id.spinnerCpuLimit;
      Spinner spinnerCpuLimit = ViewBindings.findChildViewById(rootView, id);
      if (spinnerCpuLimit == null) {
        break missingId;
      }

      id = R.id.textFileScanResult;
      TextView textFileScanResult = ViewBindings.findChildViewById(rootView, id);
      if (textFileScanResult == null) {
        break missingId;
      }

      id = R.id.textSelectedFile;
      TextView textSelectedFile = ViewBindings.findChildViewById(rootView, id);
      if (textSelectedFile == null) {
        break missingId;
      }

      return new FragmentScanBinding((ScrollView) rootView, btnSelectFileScan, buttonStartScan,
          cardCustomScan, cardFullScan, cardQuickScan, cardScheduledScan, checkBoxBootScan,
          checkBoxCloudScan, spinnerCpuLimit, textFileScanResult, textSelectedFile);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
