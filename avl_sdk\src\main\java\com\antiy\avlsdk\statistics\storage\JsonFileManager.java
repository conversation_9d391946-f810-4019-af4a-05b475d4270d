package com.antiy.avlsdk.statistics.storage;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.statistics.entity.ScanSessionRecord;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * JSON文件管理器
 * 负责扫描记录的JSON文件读写操作
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class JsonFileManager {
    
    private static final String TAG = "JsonFileManager";
    
    /** 记录存储根目录 */
    private File rootDirectory;
    
    /** 日期格式化器 */
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy/MM", Locale.getDefault());
    
    public JsonFileManager(File rootDirectory) {
        this.rootDirectory = rootDirectory;
    }
    
    /**
     * 保存扫描记录到JSON文件
     */
    public String saveRecord(ScanSessionRecord record) {
        if (record == null) {
            return null;
        }
        
        try {
            // 创建目录结构：scan_records/2024/12/
            File dateDir = createDateDirectory(record.startTime);
            if (dateDir == null) {
                return null;
            }
            
            // 生成文件名：scan_20241204_143022_001.json
            String fileName = record.scanId + ".json";
            File jsonFile = new File(dateDir, fileName);
            
            // 转换为JSON并保存
            JSONObject jsonObject = recordToJson(record);
            if (saveJsonToFile(jsonObject, jsonFile)) {
                return jsonFile.getAbsolutePath();
            }
            
            return null;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to save record " + record.scanId + ": " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 从JSON文件加载扫描记录
     */
    public ScanSessionRecord loadRecord(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return null;
        }
        
        try {
            File jsonFile = new File(filePath);
            if (!jsonFile.exists()) {
                AVLEngine.Logger.warn(TAG + ": JSON file not found: " + filePath);
                return null;
            }
            
            JSONObject jsonObject = loadJsonFromFile(jsonFile);
            if (jsonObject == null) {
                return null;
            }
            
            return jsonToRecord(jsonObject);
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to load record from " + filePath + ": " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 删除JSON文件
     */
    public boolean deleteRecord(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        
        try {
            File jsonFile = new File(filePath);
            if (jsonFile.exists()) {
                return jsonFile.delete();
            }
            return true; // 文件不存在也算删除成功
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to delete file " + filePath + ": " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 清理空目录
     */
    public void cleanupEmptyDirectories() {
        try {
            cleanupEmptyDirectoriesRecursive(rootDirectory);
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to cleanup empty directories: " + e.getMessage());
        }
    }

    /**
     * 清空所有记录文件
     */
    public boolean clearAllRecords() {
        try {
            return deleteDirectoryRecursive(rootDirectory);
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to clear all records: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 创建日期目录
     */
    private File createDateDirectory(long timestamp) {
        try {
            String datePath = DATE_FORMAT.format(new Date(timestamp));
            File dateDir = new File(rootDirectory, datePath);
            
            if (!dateDir.exists()) {
                if (!dateDir.mkdirs()) {
                    AVLEngine.Logger.error(TAG + ": Failed to create date directory: " + dateDir.getAbsolutePath());
                    return null;
                }
            }
            
            return dateDir;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to create date directory: " + e.getMessage());
            return null;
        }
    }

    /**
     * 递归删除目录及其所有内容
     */
    private boolean deleteDirectoryRecursive(File directory) {
        if (directory == null || !directory.exists()) {
            return true;
        }

        try {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectoryRecursive(file);
                    } else {
                        file.delete();
                    }
                }
            }

            // 重新创建根目录，保持目录结构
            if (directory.equals(rootDirectory)) {
                AVLEngine.Logger.info(TAG + ": Cleared all files in root directory: " + directory.getAbsolutePath());
                return true;
            } else {
                return directory.delete();
            }

        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to delete directory " + directory.getAbsolutePath() + ": " + e.getMessage());
            return false;
        }
    }

    /**
     * 将扫描记录转换为JSON对象
     */
    private JSONObject recordToJson(ScanSessionRecord record) throws JSONException {
        JSONObject json = new JSONObject();
        
        // 基本信息
        json.put("scanId", record.scanId);
        json.put("scanType", record.scanType);
        json.put("startTime", record.startTime);
        json.put("endTime", record.endTime);
        json.put("duration", record.duration);
        json.put("status", record.status);
        json.put("scanPath", record.scanPath);
        json.put("createdAt", record.createdAt);
        
        if (record.errorMessage != null) {
            json.put("errorMessage", record.errorMessage);
        }
        
        // 统计数据
        if (record.statistics != null) {
            JSONObject statsJson = new JSONObject();
            statsJson.put("totalFiles", record.statistics.totalFiles);
            statsJson.put("scannedFiles", record.statistics.scannedFiles);
            statsJson.put("skippedFiles", record.statistics.skippedFiles);
            statsJson.put("maliciousFiles", record.statistics.maliciousFiles);
            statsJson.put("cleanFiles", record.statistics.cleanFiles);
            statsJson.put("errorFiles", record.statistics.errorFiles);
            statsJson.put("cacheHitFiles", record.statistics.cacheHitFiles);
            statsJson.put("cloudScanFiles", record.statistics.cloudScanFiles);
            statsJson.put("localScanFiles", record.statistics.localScanFiles);
            statsJson.put("totalScanTime", record.statistics.totalScanTime);
            statsJson.put("avgScanTimePerFile", record.statistics.avgScanTimePerFile);
            statsJson.put("maxScanTimePerFile", record.statistics.maxScanTimePerFile);
            statsJson.put("minScanTimePerFile", record.statistics.minScanTimePerFile);
            json.put("statistics", statsJson);
        }
        
        // 引擎统计
        if (record.engineStats != null) {
            JSONObject engineJson = new JSONObject();
            engineJson.put("mobileEngine", engineStatsToJson(record.engineStats.mobileEngine));
            engineJson.put("pcEngine", engineStatsToJson(record.engineStats.pcEngine));
            engineJson.put("cloudEngine", engineStatsToJson(record.engineStats.cloudEngine));
            json.put("engineStats", engineJson);
        }
        
        // 威胁信息
        if (record.threats != null && !record.threats.isEmpty()) {
            JSONArray threatsArray = new JSONArray();
            for (int i = 0; i < record.threats.size(); i++) {
                threatsArray.put(threatInfoToJson(record.threats.get(i)));
            }
            json.put("threats", threatsArray);
        }
        
        // 性能数据
        if (record.performance != null) {
            JSONObject perfJson = new JSONObject();
            perfJson.put("peakMemoryUsage", record.performance.peakMemoryUsage);
            perfJson.put("avgMemoryUsage", record.performance.avgMemoryUsage);
            perfJson.put("avgCpuUsage", record.performance.avgCpuUsage);
            perfJson.put("peakCpuUsage", record.performance.peakCpuUsage);
            perfJson.put("ioOperations", record.performance.ioOperations);
            perfJson.put("networkRequests", record.performance.networkRequests);
            perfJson.put("networkBytes", record.performance.networkBytes);
            perfJson.put("cacheHits", record.performance.cacheHits);
            perfJson.put("cacheMisses", record.performance.cacheMisses);
            json.put("performance", perfJson);
        }
        
        return json;
    }
    
    /**
     * 引擎统计转JSON
     */
    private JSONObject engineStatsToJson(com.antiy.avlsdk.statistics.entity.EngineStatistics.EngineStats stats) throws JSONException {
        JSONObject json = new JSONObject();
        json.put("engineName", stats.engineName);
        json.put("filesScanned", stats.filesScanned);
        json.put("threatsFound", stats.threatsFound);
        json.put("totalScanTime", stats.totalScanTime);
        json.put("avgScanTime", stats.avgScanTime);
        json.put("maxScanTime", stats.maxScanTime);
        json.put("minScanTime", stats.minScanTime);
        json.put("threatDetectionRate", stats.threatDetectionRate);
        return json;
    }
    
    /**
     * 威胁信息转JSON
     */
    private JSONObject threatInfoToJson(com.antiy.avlsdk.statistics.entity.ThreatInfo threat) throws JSONException {
        JSONObject json = new JSONObject();
        json.put("filePath", threat.filePath);
        json.put("fileName", threat.fileName);
        json.put("virusName", threat.virusName);
        json.put("sha256", threat.sha256);
        json.put("fileSize", threat.fileSize);
        json.put("detectedTime", threat.detectedTime);
        json.put("engine", threat.engine);
        json.put("isCloudScan", threat.isCloudScan);
        json.put("scanTime", threat.scanTime);
        json.put("riskLevel", threat.riskLevel);
        json.put("actionStatus", threat.actionStatus);
        if (threat.description != null) {
            json.put("description", threat.description);
        }
        return json;
    }
    
    /**
     * 保存JSON对象到文件
     */
    private boolean saveJsonToFile(JSONObject jsonObject, File file) {
        BufferedWriter writer = null;
        try {
            writer = new BufferedWriter(new OutputStreamWriter(
                    new FileOutputStream(file), StandardCharsets.UTF_8));
            writer.write(jsonObject.toString(2)); // 格式化输出，缩进2个空格
            writer.flush();
            return true;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to save JSON to file " + file.getAbsolutePath() + ": " + e.getMessage());
            return false;
            
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
        }
    }
    
    /**
     * 从文件加载JSON对象
     */
    private JSONObject loadJsonFromFile(File file) {
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new InputStreamReader(
                    new FileInputStream(file), StandardCharsets.UTF_8));
            
            StringBuilder content = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line);
            }
            
            return new JSONObject(content.toString());
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to load JSON from file " + file.getAbsolutePath() + ": " + e.getMessage());
            return null;
            
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
        }
    }
    
    /**
     * JSON转扫描记录（简化版本，只实现基本字段）
     */
    private ScanSessionRecord jsonToRecord(JSONObject json) throws JSONException {
        ScanSessionRecord record = new ScanSessionRecord();
        
        // 基本信息
        record.scanId = json.optString("scanId");
        record.scanType = json.optString("scanType");
        record.startTime = json.optLong("startTime");
        record.endTime = json.optLong("endTime");
        record.duration = json.optLong("duration");
        record.status = json.optString("status");
        record.scanPath = json.optString("scanPath");
        record.createdAt = json.optLong("createdAt");
        record.errorMessage = json.optString("errorMessage", null);
        
        // 反序列化统计数据
        if (json.has("statistics")) {
            JSONObject statsJson = json.getJSONObject("statistics");
            record.statistics.totalFiles = statsJson.optInt("totalFiles", 0);
            record.statistics.scannedFiles = statsJson.optInt("scannedFiles", 0);
            record.statistics.skippedFiles = statsJson.optInt("skippedFiles", 0);
            record.statistics.maliciousFiles = statsJson.optInt("maliciousFiles", 0);
            record.statistics.cleanFiles = statsJson.optInt("cleanFiles", 0);
            record.statistics.errorFiles = statsJson.optInt("errorFiles", 0);
            record.statistics.cacheHitFiles = statsJson.optInt("cacheHitFiles", 0);
            record.statistics.totalScanTime = statsJson.optLong("totalScanTime", 0);
            record.statistics.avgScanTimePerFile = statsJson.optDouble("avgScanTimePerFile", 0.0);
            record.statistics.minScanTimePerFile = statsJson.optLong("minScanTimePerFile", 0);
            record.statistics.maxScanTimePerFile = statsJson.optLong("maxScanTimePerFile", 0);

            AVLEngine.Logger.info(TAG + ": Loaded statistics - totalFiles: " + record.statistics.totalFiles +
                                 ", maliciousFiles: " + record.statistics.maliciousFiles);
        } else {
            AVLEngine.Logger.warn(TAG + ": No statistics data found in JSON for " + record.scanId);
        }

        // 反序列化引擎统计数据
        if (json.has("engineStats")) {
            JSONObject engineJson = json.getJSONObject("engineStats");
            if (engineJson.has("mobileEngine")) {
                JSONObject mobileJson = engineJson.getJSONObject("mobileEngine");
                record.engineStats.mobileEngine.filesScanned = mobileJson.optInt("filesScanned", 0);
                record.engineStats.mobileEngine.threatsFound = mobileJson.optInt("threatsFound", 0);
                record.engineStats.mobileEngine.totalScanTime = mobileJson.optLong("totalScanTime", 0);
            }
            if (engineJson.has("pcEngine")) {
                JSONObject pcJson = engineJson.getJSONObject("pcEngine");
                record.engineStats.pcEngine.filesScanned = pcJson.optInt("filesScanned", 0);
                record.engineStats.pcEngine.threatsFound = pcJson.optInt("threatsFound", 0);
                record.engineStats.pcEngine.totalScanTime = pcJson.optLong("totalScanTime", 0);
            }
            if (engineJson.has("cloudEngine")) {
                JSONObject cloudJson = engineJson.getJSONObject("cloudEngine");
                record.engineStats.cloudEngine.filesScanned = cloudJson.optInt("filesScanned", 0);
                record.engineStats.cloudEngine.threatsFound = cloudJson.optInt("threatsFound", 0);
                record.engineStats.cloudEngine.totalScanTime = cloudJson.optLong("totalScanTime", 0);
            }
        }

        return record;
    }
    
    /**
     * 递归清理空目录
     */
    private void cleanupEmptyDirectoriesRecursive(File directory) {
        if (directory == null || !directory.isDirectory()) {
            return;
        }
        
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    cleanupEmptyDirectoriesRecursive(file);
                }
            }
        }
        
        // 检查目录是否为空，如果为空且不是根目录则删除
        files = directory.listFiles();
        if (files != null && files.length == 0 && !directory.equals(rootDirectory)) {
            directory.delete();
        }
    }
}
