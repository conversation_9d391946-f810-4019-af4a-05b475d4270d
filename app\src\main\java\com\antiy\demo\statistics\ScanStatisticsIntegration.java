package com.antiy.demo.statistics;

import android.content.Context;
import android.util.Log;

import com.antiy.avlsdk.AVLEngine;

import java.util.List;

/**
 * 扫描统计集成适配器
 * 
 * 提供简化的API来启用/禁用统计功能，并提供便捷的查询方法。
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ScanStatisticsIntegration {
    
    private static final String TAG = "ScanStatisticsIntegration";
    
    private static AppScanStatisticsManager statisticsManager;
    private static boolean isEnabled = false;
    
    /**
     * 启用统计功能（使用默认配置）
     * 
     * @param context 应用上下文
     * @return 是否启用成功
     */
    public static boolean enableStatistics(Context context) {
        StatisticsConfig config = StatisticsConfig.getDefault(context);
        return enableStatistics(context, config);
    }
    
    /**
     * 启用统计功能（使用自定义配置）
     * 
     * @param context 应用上下文
     * @param config 统计配置
     * @return 是否启用成功
     */
    public static boolean enableStatistics(Context context, StatisticsConfig config) {
        if (isEnabled) {
            Log.w(TAG, "Statistics already enabled");
            return true;
        }
        
        try {
            statisticsManager = new AppScanStatisticsManager(context, config);
            AVLEngine.getInstance().setStatisticsCallback(statisticsManager);
            isEnabled = true;
            
            Log.i(TAG, "Statistics enabled successfully with config: " + config.toString());
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to enable statistics", e);
            return false;
        }
    }
    
    /**
     * 禁用统计功能
     */
    public static void disableStatistics() {
        if (!isEnabled) {
            Log.d(TAG, "Statistics already disabled");
            return;
        }
        
        try {
            AVLEngine.getInstance().removeStatisticsCallback();
            statisticsManager = null;
            isEnabled = false;
            
            Log.i(TAG, "Statistics disabled successfully");
            
        } catch (Exception e) {
            Log.e(TAG, "Error disabling statistics", e);
        }
    }
    
    /**
     * 获取统计管理器
     * 
     * @return 统计管理器实例，如果未启用则返回null
     */
    public static AppScanStatisticsManager getStatisticsManager() {
        return statisticsManager;
    }
    
    /**
     * 检查统计功能是否启用
     * 
     * @return true表示已启用，false表示未启用
     */
    public static boolean isEnabled() {
        return isEnabled && statisticsManager != null;
    }
    
    /**
     * 检查SDK是否已初始化，如果是则自动启用统计功能
     * 
     * @param context 应用上下文
     * @return 是否成功启用统计功能
     */
    public static boolean autoEnableIfSDKReady(Context context) {
        // 检查SDK是否已初始化
        if (AVLEngine.getInstance().getInitResult() != null && 
            AVLEngine.getInstance().getInitResult().isSuccess) {
            
            if (!isEnabled()) {
                StatisticsConfig config = StatisticsConfig.getDefault(context);
                return enableStatistics(context, config);
            }
            return true;
        } else {
            Log.w(TAG, "SDK not initialized, cannot enable statistics");
            return false;
        }
    }
    
    /**
     * 检查SDK和统计功能的整体状态
     * 
     * @return true表示SDK已初始化且统计功能已启用
     */
    public static boolean isFullyReady() {
        return AVLEngine.getInstance().getInitResult() != null && 
               AVLEngine.getInstance().getInitResult().isSuccess && 
               isEnabled();
    }
    
    // ==================== 便捷查询方法 ====================
    
    /**
     * 获取最近的扫描记录
     * 
     * @param limit 记录数量限制
     * @return 扫描记录列表，如果统计功能未启用则返回null
     */
    public static List<ScanSessionRecord> getRecentScans(int limit) {
        if (!isEnabled()) {
            Log.w(TAG, "Statistics not enabled");
            return null;
        }
        return statisticsManager.getRecentScans(limit);
    }
    
    /**
     * 获取指定时间范围的扫描记录
     * 
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 扫描记录列表，如果统计功能未启用则返回null
     */
    public static List<ScanSessionRecord> getScansByDateRange(long startTime, long endTime) {
        if (!isEnabled()) {
            Log.w(TAG, "Statistics not enabled");
            return null;
        }
        return statisticsManager.getScansByDateRange(startTime, endTime);
    }
    
    /**
     * 导出最近几天的扫描统计到JSON文件
     * 
     * @param filePath 导出文件路径
     * @param days 导出最近几天的数据
     * @return 导出结果描述，如果统计功能未启用则返回null
     */
    public static String exportRecentScansToJson(String filePath, int days) {
        if (!isEnabled()) {
            Log.w(TAG, "Statistics not enabled");
            return "Statistics not enabled";
        }
        return statisticsManager.exportToJson(filePath, days);
    }
    
    /**
     * 导出最近几天的扫描统计到CSV文件
     * 
     * @param filePath 导出文件路径
     * @param days 导出最近几天的数据
     * @return 导出结果描述，如果统计功能未启用则返回null
     */
    public static String exportRecentScansToCsv(String filePath, int days) {
        if (!isEnabled()) {
            Log.w(TAG, "Statistics not enabled");
            return "Statistics not enabled";
        }
        return statisticsManager.exportToCsv(filePath, days);
    }
    
    /**
     * 获取统计功能状态信息
     * 
     * @return 状态信息字符串
     */
    public static String getStatusInfo() {
        StringBuilder status = new StringBuilder();
        status.append("Statistics Enabled: ").append(isEnabled()).append("\n");
        
        if (isEnabled() && statisticsManager != null) {
            status.append("Active Sessions: ").append(statisticsManager.getActiveSessionCount()).append("\n");
            status.append("Config: ").append(statisticsManager.getConfig().toString()).append("\n");
        }
        
        status.append("SDK Initialized: ").append(AVLEngine.getInstance().isInitialized()).append("\n");
        status.append("Fully Ready: ").append(isFullyReady());
        
        return status.toString();
    }
    
    /**
     * 获取最近扫描的简要摘要
     * 
     * @param limit 记录数量限制
     * @return 摘要字符串
     */
    public static String getRecentScansSummary(int limit) {
        if (!isEnabled()) {
            return "Statistics not enabled";
        }
        
        try {
            List<ScanSessionRecord> records = statisticsManager.getRecentScans(limit);
            if (records == null || records.isEmpty()) {
                return "No scan records found";
            }
            
            StringBuilder summary = new StringBuilder();
            summary.append("Recent ").append(records.size()).append(" scans:\n");
            
            for (int i = 0; i < Math.min(records.size(), limit); i++) {
                ScanSessionRecord record = records.get(i);
                summary.append(String.format("%d. %s - %s (%d files, %d threats)\n",
                    i + 1,
                    record.getSessionId().substring(0, Math.min(8, record.getSessionId().length())),
                    record.getStatus(),
                    record.getTotalFiles(),
                    record.getThreatsFound()));
            }
            
            return summary.toString();
            
        } catch (Exception e) {
            Log.e(TAG, "Error generating recent scans summary", e);
            return "Error generating summary: " + e.getMessage();
        }
    }
    
    /**
     * 获取今日扫描统计摘要
     * 
     * @return 今日统计摘要
     */
    public static String getTodayStatisticsSummary() {
        if (!isEnabled()) {
            return "Statistics not enabled";
        }
        
        try {
            long todayStart = System.currentTimeMillis() - (System.currentTimeMillis() % (24 * 60 * 60 * 1000));
            long todayEnd = todayStart + (24 * 60 * 60 * 1000);
            
            List<ScanSessionRecord> todayRecords = statisticsManager.getScansByDateRange(todayStart, todayEnd);
            
            if (todayRecords == null || todayRecords.isEmpty()) {
                return "No scans today";
            }
            
            int totalScans = todayRecords.size();
            int totalFiles = 0;
            int totalThreats = 0;
            
            for (ScanSessionRecord record : todayRecords) {
                totalFiles += record.getTotalFiles();
                totalThreats += record.getThreatsFound();
            }
            
            return String.format("Today: %d scans, %d files, %d threats found", 
                totalScans, totalFiles, totalThreats);
            
        } catch (Exception e) {
            Log.e(TAG, "Error generating today's statistics summary", e);
            return "Error generating today's summary: " + e.getMessage();
        }
    }
}
