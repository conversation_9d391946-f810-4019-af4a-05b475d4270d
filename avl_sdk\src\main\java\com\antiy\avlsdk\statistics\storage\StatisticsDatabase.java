package com.antiy.avlsdk.statistics.storage;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.statistics.entity.ScanSessionRecord;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 统计数据库
 * 负责扫描统计数据的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class StatisticsDatabase extends SQLiteOpenHelper {
    
    private static final String TAG = "StatisticsDatabase";
    
    private static final String DATABASE_NAME = "scan_statistics.db";
    private static final int DATABASE_VERSION = 1;
    
    // 扫描会话表
    private static final String TABLE_SCAN_SESSIONS = "scan_sessions";
    private static final String COLUMN_SCAN_ID = "scan_id";
    private static final String COLUMN_SCAN_TYPE = "scan_type";
    private static final String COLUMN_START_TIME = "start_time";
    private static final String COLUMN_END_TIME = "end_time";
    private static final String COLUMN_DURATION = "duration";
    private static final String COLUMN_TOTAL_FILES = "total_files";
    private static final String COLUMN_MALICIOUS_FILES = "malicious_files";
    private static final String COLUMN_JSON_FILE_PATH = "json_file_path";
    private static final String COLUMN_STATUS = "status";
    private static final String COLUMN_CREATED_AT = "created_at";
    
    // 扫描统计表
    private static final String TABLE_SCAN_STATISTICS = "scan_statistics";
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_DATE = "date";
    private static final String COLUMN_HOUR = "hour";
    private static final String COLUMN_SCAN_DURATION = "scan_duration";
    private static final String COLUMN_ENGINE_MOBILE_FILES = "engine_mobile_files";
    private static final String COLUMN_ENGINE_PC_FILES = "engine_pc_files";
    
    // 威胁汇总表
    private static final String TABLE_THREAT_SUMMARY = "threat_summary";
    private static final String COLUMN_VIRUS_NAME = "virus_name";
    private static final String COLUMN_FILE_COUNT = "file_count";
    private static final String COLUMN_FIRST_DETECTED = "first_detected";
    private static final String COLUMN_LAST_DETECTED = "last_detected";
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
    
    public StatisticsDatabase(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }
    
    @Override
    public void onCreate(SQLiteDatabase db) {
        // 创建扫描会话表
        String createSessionsTable = "CREATE TABLE " + TABLE_SCAN_SESSIONS + " (" +
                COLUMN_SCAN_ID + " TEXT PRIMARY KEY, " +
                COLUMN_SCAN_TYPE + " TEXT NOT NULL, " +
                COLUMN_START_TIME + " INTEGER NOT NULL, " +
                COLUMN_END_TIME + " INTEGER, " +
                COLUMN_DURATION + " INTEGER, " +
                COLUMN_TOTAL_FILES + " INTEGER DEFAULT 0, " +
                COLUMN_MALICIOUS_FILES + " INTEGER DEFAULT 0, " +
                COLUMN_JSON_FILE_PATH + " TEXT NOT NULL, " +
                COLUMN_STATUS + " TEXT DEFAULT 'RUNNING', " +
                COLUMN_CREATED_AT + " INTEGER DEFAULT (strftime('%s','now') * 1000)" +
                ")";
        db.execSQL(createSessionsTable);
        
        // 创建扫描统计表
        String createStatisticsTable = "CREATE TABLE " + TABLE_SCAN_STATISTICS + " (" +
                COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                COLUMN_SCAN_ID + " TEXT NOT NULL, " +
                COLUMN_DATE + " TEXT NOT NULL, " +
                COLUMN_HOUR + " INTEGER NOT NULL, " +
                COLUMN_TOTAL_FILES + " INTEGER DEFAULT 0, " +
                COLUMN_MALICIOUS_FILES + " INTEGER DEFAULT 0, " +
                COLUMN_SCAN_DURATION + " INTEGER DEFAULT 0, " +
                COLUMN_ENGINE_MOBILE_FILES + " INTEGER DEFAULT 0, " +
                COLUMN_ENGINE_PC_FILES + " INTEGER DEFAULT 0, " +
                "FOREIGN KEY (" + COLUMN_SCAN_ID + ") REFERENCES " + TABLE_SCAN_SESSIONS + "(" + COLUMN_SCAN_ID + ")" +
                ")";
        db.execSQL(createStatisticsTable);
        
        // 创建威胁汇总表
        String createThreatTable = "CREATE TABLE " + TABLE_THREAT_SUMMARY + " (" +
                COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                COLUMN_SCAN_ID + " TEXT NOT NULL, " +
                COLUMN_VIRUS_NAME + " TEXT NOT NULL, " +
                COLUMN_FILE_COUNT + " INTEGER DEFAULT 1, " +
                COLUMN_FIRST_DETECTED + " INTEGER NOT NULL, " +
                COLUMN_LAST_DETECTED + " INTEGER NOT NULL, " +
                "FOREIGN KEY (" + COLUMN_SCAN_ID + ") REFERENCES " + TABLE_SCAN_SESSIONS + "(" + COLUMN_SCAN_ID + ")" +
                ")";
        db.execSQL(createThreatTable);
        
        // 创建索引
        db.execSQL("CREATE INDEX idx_sessions_start_time ON " + TABLE_SCAN_SESSIONS + "(" + COLUMN_START_TIME + ")");
        db.execSQL("CREATE INDEX idx_statistics_date ON " + TABLE_SCAN_STATISTICS + "(" + COLUMN_DATE + ")");
        db.execSQL("CREATE INDEX idx_threat_virus_name ON " + TABLE_THREAT_SUMMARY + "(" + COLUMN_VIRUS_NAME + ")");
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        // 简单的升级策略：删除旧表，创建新表
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_THREAT_SUMMARY);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_SCAN_STATISTICS);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_SCAN_SESSIONS);
        onCreate(db);
    }
    
    /**
     * 保存扫描会话记录
     */
    public boolean saveSessionRecord(ScanSessionRecord record, String jsonFilePath) {
        if (record == null || jsonFilePath == null) {
            return false;
        }
        
        SQLiteDatabase db = null;
        try {
            db = this.getWritableDatabase();
            
            ContentValues values = new ContentValues();
            values.put(COLUMN_SCAN_ID, record.scanId);
            values.put(COLUMN_SCAN_TYPE, record.scanType);
            values.put(COLUMN_START_TIME, record.startTime);
            values.put(COLUMN_END_TIME, record.endTime);
            values.put(COLUMN_DURATION, record.duration);
            values.put(COLUMN_JSON_FILE_PATH, jsonFilePath);
            values.put(COLUMN_STATUS, record.status);
            values.put(COLUMN_CREATED_AT, record.createdAt);
            
            if (record.statistics != null) {
                values.put(COLUMN_TOTAL_FILES, record.statistics.totalFiles);
                values.put(COLUMN_MALICIOUS_FILES, record.statistics.maliciousFiles);
            }
            
            long result = db.replace(TABLE_SCAN_SESSIONS, null, values);
            
            if (result != -1) {
                // 保存统计数据
                saveStatisticsRecord(db, record);
                // 保存威胁汇总
                saveThreatSummary(db, record);
            }
            
            return result != -1;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to save session record: " + e.getMessage());
            return false;
        } finally {
            if (db != null) {
                db.close();
            }
        }
    }
    
    /**
     * 保存统计记录
     */
    private void saveStatisticsRecord(SQLiteDatabase db, ScanSessionRecord record) {
        if (record.statistics == null) {
            return;
        }
        
        try {
            Date startDate = new Date(record.startTime);
            String dateStr = DATE_FORMAT.format(startDate);
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            calendar.setTime(startDate);
            int hour = calendar.get(java.util.Calendar.HOUR_OF_DAY);
            
            ContentValues values = new ContentValues();
            values.put(COLUMN_SCAN_ID, record.scanId);
            values.put(COLUMN_DATE, dateStr);
            values.put(COLUMN_HOUR, hour);
            values.put(COLUMN_TOTAL_FILES, record.statistics.totalFiles);
            values.put(COLUMN_MALICIOUS_FILES, record.statistics.maliciousFiles);
            values.put(COLUMN_SCAN_DURATION, record.duration);

            // 添加调试日志
            AVLEngine.Logger.info(TAG + ": Saving statistics - scanId: " + record.scanId +
                                 ", totalFiles: " + record.statistics.totalFiles +
                                 ", maliciousFiles: " + record.statistics.maliciousFiles +
                                 ", duration: " + record.duration);

            if (record.engineStats != null) {
                values.put(COLUMN_ENGINE_MOBILE_FILES, record.engineStats.mobileEngine.filesScanned);
                values.put(COLUMN_ENGINE_PC_FILES, record.engineStats.pcEngine.filesScanned);
            }

            long rowId = db.insert(TABLE_SCAN_STATISTICS, null, values);

            AVLEngine.Logger.info(TAG + ": Saved statistics record for session: " + record.scanId + ", rowId: " + rowId);

            // 立即验证保存的数据
            if (rowId > 0) {
                verifyStatisticsRecord(db, record.scanId, record.statistics.totalFiles, record.statistics.maliciousFiles);
            }

        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to save statistics record: " + e.getMessage());
        }
    }

    /**
     * 验证统计记录是否正确保存
     */
    private void verifyStatisticsRecord(SQLiteDatabase db, String scanId, int expectedTotalFiles, int expectedMaliciousFiles) {
        Cursor cursor = null;
        try {
            cursor = db.query(TABLE_SCAN_STATISTICS,
                new String[]{COLUMN_TOTAL_FILES, COLUMN_MALICIOUS_FILES},
                COLUMN_SCAN_ID + "=?",
                new String[]{scanId},
                null, null, null);

            if (cursor != null && cursor.moveToFirst()) {
                int actualTotalFiles = cursor.getInt(0);
                int actualMaliciousFiles = cursor.getInt(1);

                AVLEngine.Logger.info(TAG + ": Verification for " + scanId +
                    " - Expected: totalFiles=" + expectedTotalFiles + ", maliciousFiles=" + expectedMaliciousFiles +
                    " - Actual: totalFiles=" + actualTotalFiles + ", maliciousFiles=" + actualMaliciousFiles);

                if (actualTotalFiles != expectedTotalFiles || actualMaliciousFiles != expectedMaliciousFiles) {
                    AVLEngine.Logger.error(TAG + ": DATA MISMATCH detected for session " + scanId);
                }
            } else {
                AVLEngine.Logger.error(TAG + ": Failed to find saved record for verification: " + scanId);
            }
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Error during verification: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }

    /**
     * 保存威胁汇总
     */
    private void saveThreatSummary(SQLiteDatabase db, ScanSessionRecord record) {
        if (record.threats == null || record.threats.isEmpty()) {
            return;
        }
        
        try {
            for (int i = 0; i < record.threats.size(); i++) {
                com.antiy.avlsdk.statistics.entity.ThreatInfo threat = record.threats.get(i);
                
                ContentValues values = new ContentValues();
                values.put(COLUMN_SCAN_ID, record.scanId);
                values.put(COLUMN_VIRUS_NAME, threat.virusName);
                values.put(COLUMN_FILE_COUNT, 1);
                values.put(COLUMN_FIRST_DETECTED, threat.detectedTime);
                values.put(COLUMN_LAST_DETECTED, threat.detectedTime);
                
                db.insert(TABLE_THREAT_SUMMARY, null, values);
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to save threat summary: " + e.getMessage());
        }
    }
    
    /**
     * 获取JSON文件路径
     */
    public String getJsonFilePath(String scanId) {
        SQLiteDatabase db = null;
        Cursor cursor = null;
        try {
            db = this.getReadableDatabase();
            cursor = db.query(TABLE_SCAN_SESSIONS,
                    new String[]{COLUMN_JSON_FILE_PATH},
                    COLUMN_SCAN_ID + "=?",
                    new String[]{scanId},
                    null, null, null);
            
            if (cursor != null && cursor.moveToFirst()) {
                return cursor.getString(0);
            }
            
            return null;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get JSON file path: " + e.getMessage());
            return null;
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            if (db != null) {
                db.close();
            }
        }
    }
    
    /**
     * 获取最近的扫描ID列表
     */
    public List<String> getRecentScanIds(int limit) {
        List<String> scanIds = new ArrayList<>();
        SQLiteDatabase db = null;
        Cursor cursor = null;
        
        try {
            db = this.getReadableDatabase();
            cursor = db.query(TABLE_SCAN_SESSIONS,
                    new String[]{COLUMN_SCAN_ID},
                    null, null, null, null,
                    COLUMN_START_TIME + " DESC",
                    String.valueOf(limit));
            
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    scanIds.add(cursor.getString(0));
                }
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get recent scan IDs: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            if (db != null) {
                db.close();
            }
        }
        
        return scanIds;
    }
    
    /**
     * 根据日期获取扫描ID列表
     */
    public List<String> getScanIdsByDate(String date) {
        List<String> scanIds = new ArrayList<>();
        SQLiteDatabase db = null;
        Cursor cursor = null;
        
        try {
            db = this.getReadableDatabase();
            cursor = db.query(TABLE_SCAN_STATISTICS,
                    new String[]{COLUMN_SCAN_ID},
                    COLUMN_DATE + "=?",
                    new String[]{date},
                    null, null,
                    COLUMN_SCAN_ID + " DESC");
            
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    scanIds.add(cursor.getString(0));
                }
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get scan IDs by date: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            if (db != null) {
                db.close();
            }
        }
        
        return scanIds;
    }
    
    /**
     * 根据时间范围获取扫描ID列表
     */
    public List<String> getScanIdsByTimeRange(long startTime, long endTime) {
        List<String> scanIds = new ArrayList<>();
        SQLiteDatabase db = null;
        Cursor cursor = null;
        
        try {
            db = this.getReadableDatabase();
            cursor = db.query(TABLE_SCAN_SESSIONS,
                    new String[]{COLUMN_SCAN_ID},
                    COLUMN_START_TIME + ">=? AND " + COLUMN_START_TIME + "<=?",
                    new String[]{String.valueOf(startTime), String.valueOf(endTime)},
                    null, null,
                    COLUMN_START_TIME + " DESC");
            
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    scanIds.add(cursor.getString(0));
                }
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get scan IDs by time range: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            if (db != null) {
                db.close();
            }
        }
        
        return scanIds;
    }
    
    /**
     * 删除扫描会话记录
     */
    public boolean deleteSessionRecord(String scanId) {
        SQLiteDatabase db = null;
        try {
            db = this.getWritableDatabase();
            
            // 删除相关的统计记录
            db.delete(TABLE_THREAT_SUMMARY, COLUMN_SCAN_ID + "=?", new String[]{scanId});
            db.delete(TABLE_SCAN_STATISTICS, COLUMN_SCAN_ID + "=?", new String[]{scanId});
            
            // 删除会话记录
            int result = db.delete(TABLE_SCAN_SESSIONS, COLUMN_SCAN_ID + "=?", new String[]{scanId});
            
            return result > 0;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to delete session record: " + e.getMessage());
            return false;
        } finally {
            if (db != null) {
                db.close();
            }
        }
    }

    /**
     * 清空所有统计数据
     */
    public boolean clearAllStatistics() {
        SQLiteDatabase db = null;
        try {
            db = this.getWritableDatabase();

            // 删除所有统计相关的表数据
            db.delete(TABLE_THREAT_SUMMARY, null, null);
            db.delete(TABLE_SCAN_STATISTICS, null, null);
            db.delete(TABLE_SCAN_SESSIONS, null, null);

            AVLEngine.Logger.info(TAG + ": Cleared all statistics data from database");
            return true;

        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to clear all statistics: " + e.getMessage());
            return false;
        } finally {
            if (db != null) {
                db.close();
            }
        }
    }

    /**
     * 获取过期的扫描ID列表
     */
    public List<String> getExpiredScanIds(long cutoffTime) {
        List<String> scanIds = new ArrayList<>();
        SQLiteDatabase db = null;
        Cursor cursor = null;
        
        try {
            db = this.getReadableDatabase();
            cursor = db.query(TABLE_SCAN_SESSIONS,
                    new String[]{COLUMN_SCAN_ID},
                    COLUMN_START_TIME + "<?",
                    new String[]{String.valueOf(cutoffTime)},
                    null, null, null);
            
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    scanIds.add(cursor.getString(0));
                }
            }
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get expired scan IDs: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            if (db != null) {
                db.close();
            }
        }
        
        return scanIds;
    }
    
    /**
     * 获取记录总数
     */
    public int getTotalRecordCount() {
        SQLiteDatabase db = null;
        Cursor cursor = null;
        try {
            db = this.getReadableDatabase();
            cursor = db.rawQuery("SELECT COUNT(*) FROM " + TABLE_SCAN_SESSIONS, null);
            
            if (cursor != null && cursor.moveToFirst()) {
                return cursor.getInt(0);
            }
            
            return 0;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get total record count: " + e.getMessage());
            return 0;
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            if (db != null) {
                db.close();
            }
        }
    }
    
    /**
     * 获取记录时间范围
     */
    public long[] getRecordTimeRange() {
        SQLiteDatabase db = null;
        Cursor cursor = null;
        try {
            db = this.getReadableDatabase();
            cursor = db.rawQuery("SELECT MIN(" + COLUMN_START_TIME + "), MAX(" + COLUMN_START_TIME + ") FROM " + TABLE_SCAN_SESSIONS, null);
            
            if (cursor != null && cursor.moveToFirst()) {
                return new long[]{cursor.getLong(0), cursor.getLong(1)};
            }
            
            return new long[]{0, 0};
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get record time range: " + e.getMessage());
            return new long[]{0, 0};
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            if (db != null) {
                db.close();
            }
        }
    }
}
