 com.antiy.demo.base.BaseActivity1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder(androidx.appcompat.app.AppCompatActivity com.antiy.demo.base.BaseFragmentkotlin.Enum androidx.viewbinding.ViewBinding com.antiy.avlsdk.callback.Loggerandroid.app.Application-okhttp3.logging.HttpLoggingInterceptor.Logger'com.antiy.avlsdk.callback.NetworkTunnel0androidx.viewpager2.adapter.FragmentStateAdapterandroidx.fragment.app.Fragmentandroid.os.Parcelable                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            