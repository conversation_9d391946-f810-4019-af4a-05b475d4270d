package com.antiy.demo.fragment

import android.content.Intent
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.CompoundButton
import android.widget.Toast
import com.antiy.avlsdk.storage.DBConstants.TABLE_CACHE
import com.antiy.avlsdk.storage.DataManager
import com.antiy.demo.base.BaseFragment
import com.antiy.demo.databinding.FragmentSettingsBinding
import com.antiy.demo.activity.AntivirusSettingsActivity
import com.antiy.demo.activity.AboutActivity
import com.antiy.demo.activity.HelpFeedbackActivity
import com.antiy.demo.activity.CloudScanSettingsActivity
import com.antiy.demo.activity.PerformanceSettingsActivity
import com.antiy.avlsdk.statistics.integration.AVLEngineStatisticsIntegration
import androidx.appcompat.app.AlertDialog
import android.graphics.Color
import android.graphics.Typeface
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class SettingsFragment : BaseFragment<FragmentSettingsBinding>() {
    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentSettingsBinding {
        return FragmentSettingsBinding.inflate(inflater, container, false)
    }

    override fun initView() {
        // 初始化深色模式开关
        binding.switchDarkMode.setOnCheckedChangeListener { _: CompoundButton, isChecked: Boolean ->
            // 处理深色模式切换
            // 这里可以添加实际的深色模式切换逻辑
        }
    }

    override fun initListener() {
        // 用户信息点击事件
        binding.layoutUserProfile.setOnClickListener {
            // 跳转到用户信息页面
        }

        // 通知设置点击事件
        binding.layoutNotification.setOnClickListener {
            // 跳转到通知设置页面
        }

        // 语言设置点击事件
        binding.layoutLanguage.setOnClickListener {
            // 跳转到语言设置页面
        }

        // 杀毒引擎设置点击事件
        binding.layoutAntivirusEngine.setOnClickListener {
            // 跳转到杀毒引擎设置页面
            startActivity(Intent(requireContext(), AntivirusSettingsActivity::class.java))
        }

        // 云查杀设置点击事件
        binding.layoutCloudScan.setOnClickListener {
            // 跳转到云查杀设置页面
            CloudScanSettingsActivity.start(requireContext())
        }

        // 性能设置点击事件
        binding.layoutPerformance.setOnClickListener {
            // 跳转到性能设置页面
            PerformanceSettingsActivity.start(requireContext())
        }

        // 关于点击事件
        binding.layoutAbout.setOnClickListener {
            AboutActivity.start(requireContext())
        }

        // 帮助与反馈点击事件
        binding.layoutHelp.setOnClickListener {
            HelpFeedbackActivity.start(requireContext())
        }

        // 退出登录点击事件
        binding.layoutLogout.setOnClickListener {
            // 处理退出登录逻辑
        }

        // 查看统计按钮点击事件
        binding.viewStatisticsLl.setOnClickListener {
            viewStatistics()
        }

        // 导出统计按钮点击事件
        binding.exportStatisticsLl.setOnClickListener {
            exportStatistics()
        }

        // 清空缓存按钮点击事件（包括统计数据）
        binding.clearCacheLl.setOnClickListener {
            clearCacheAndStatistics()
        }

    }

    /**
     * 查看统计信息
     */
    private fun viewStatistics() {
        try {
            val recentSummary = AVLEngineStatisticsIntegration.getRecentScansSummary(5)
            val todayStats = AVLEngineStatisticsIntegration.getTodayStatistics()
            val storageStats = AVLEngineStatisticsIntegration.getStatisticsManager()?.storageStats

            showStatisticsDialog(recentSummary, todayStats, storageStats)

        } catch (e: Exception) {
            Toast.makeText(requireContext(), "获取统计信息失败：${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 显示统计信息对话框
     */
    private fun showStatisticsDialog(
        recentSummary: String,
        todayStats: String,
        storageStats: com.antiy.avlsdk.statistics.ScanRecordManager.StorageStats?
    ) {
        // 创建主容器
        val mainLayout = android.widget.LinearLayout(requireContext()).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(32, 24, 32, 24)
            setBackgroundColor(Color.WHITE)
        }

        // 标题
        val titleView = android.widget.TextView(requireContext()).apply {
            text = "📊 扫描统计信息"
            textSize = 20f
            setTypeface(null, Typeface.BOLD)
            setTextColor(Color.parseColor("#2196F3"))
            setPadding(0, 0, 0, 32)
            gravity = android.view.Gravity.CENTER
        }
        mainLayout.addView(titleView)

        // 最近扫描部分
        if (recentSummary.isNotEmpty() && !recentSummary.contains("暂无扫描记录")) {
            addSectionToLayout(mainLayout, "🕒 最近扫描", recentSummary, "#4CAF50")
        } else {
            addSectionToLayout(mainLayout, "🕒 最近扫描", "暂无扫描记录", "#9E9E9E")
        }

        // 今日统计部分
        if (todayStats.isNotEmpty() && !todayStats.contains("暂无扫描记录")) {
            addSectionToLayout(mainLayout, "📈 今日统计", todayStats, "#FF9800")
        } else {
            addSectionToLayout(mainLayout, "📈 今日统计", "今日暂无扫描记录", "#9E9E9E")
        }

        // 存储统计部分
        if (storageStats != null) {
            val storageInfo = buildString {
                append("总记录数：${storageStats.totalRecords} 条\n")
                append("存储空间：${storageStats.getFormattedSize()}\n")
                if (storageStats.earliestRecordTime > 0) {
                    val earliestDate = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault())
                        .format(java.util.Date(storageStats.earliestRecordTime))
                    append("最早记录：$earliestDate\n")
                }
                if (storageStats.latestRecordTime > 0) {
                    val latestDate = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault())
                        .format(java.util.Date(storageStats.latestRecordTime))
                    append("最新记录：$latestDate")
                }
            }
            addSectionToLayout(mainLayout, "💾 存储使用", storageInfo, "#9C27B0")
        }

        // 创建滚动视图
        val scrollView = android.widget.ScrollView(requireContext()).apply {
            addView(mainLayout)
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 0, 0, 0)
            }
        }

        // 创建对话框
        AlertDialog.Builder(requireContext())
            .setView(scrollView)
            .setPositiveButton("确定") { dialog, _ -> dialog.dismiss() }
            .setNeutralButton("导出统计") { _, _ -> exportStatistics() }
            .create()
            .apply {
                window?.setBackgroundDrawableResource(android.R.drawable.dialog_holo_light_frame)
                show()

                // 设置对话框大小
                window?.setLayout(
                    (resources.displayMetrics.widthPixels * 0.9).toInt(),
                    (resources.displayMetrics.heightPixels * 0.7).toInt()
                )
            }
    }

    /**
     * 添加统计部分到布局
     */
    private fun addSectionToLayout(
        parentLayout: android.widget.LinearLayout,
        title: String,
        content: String,
        titleColor: String
    ) {
        // 分割线
        val divider = android.view.View(requireContext()).apply {
            setBackgroundColor(Color.parseColor("#E0E0E0"))
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                2
            ).apply {
                setMargins(0, 16, 0, 16)
            }
        }
        parentLayout.addView(divider)

        // 标题
        val sectionTitle = android.widget.TextView(requireContext()).apply {
            text = title
            textSize = 16f
            setTypeface(null, Typeface.BOLD)
            setTextColor(Color.parseColor(titleColor))
            setPadding(0, 0, 0, 12)
        }
        parentLayout.addView(sectionTitle)

        // 内容
        val sectionContent = android.widget.TextView(requireContext()).apply {
            text = formatContent(content)
            textSize = 14f
            setTextColor(Color.parseColor("#424242"))
            setPadding(16, 0, 0, 16)
            setLineSpacing(4f, 1.2f)
        }
        parentLayout.addView(sectionContent)
    }

    /**
     * 格式化内容
     */
    private fun formatContent(content: String): String {
        return content.split("\n")
            .filter { it.trim().isNotEmpty() && !it.contains("===") }
            .joinToString("\n") { line ->
                when {
                    line.trim().startsWith("扫描次数：") -> "• $line"
                    line.trim().startsWith("成功扫描：") -> "• $line"
                    line.trim().startsWith("扫描文件：") -> "• $line"
                    line.trim().startsWith("发现威胁：") -> "• $line"
                    line.trim().startsWith("平均耗时：") -> "• $line"
                    line.trim().startsWith("总记录数：") -> "• $line"
                    line.trim().startsWith("存储空间：") -> "• $line"
                    line.trim().startsWith("最早记录：") -> "• $line"
                    line.trim().startsWith("最新记录：") -> "• $line"
                    line.contains("扫描完成") -> "• $line"
                    else -> line
                }
            }
    }

    /**
     * 导出统计报告
     */
    private fun exportStatistics() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val context = requireContext()
                val externalDir = context.getExternalFilesDir(null)
                if (externalDir == null) {
                    withContext(Dispatchers.Main) {
                        Toast.makeText(context, "无法访问外部存储", Toast.LENGTH_SHORT).show()
                    }
                    return@launch
                }

                val timestamp = System.currentTimeMillis()
                val jsonPath = "${externalDir.absolutePath}/scan_statistics_$timestamp.json"
                val csvPath = "${externalDir.absolutePath}/scan_statistics_$timestamp.csv"

                withContext(Dispatchers.Main) {
                    Toast.makeText(context, "开始导出统计报告...", Toast.LENGTH_SHORT).show()
                    Toast.makeText(context, File(jsonPath).parent, Toast.LENGTH_SHORT).show()
                }

                // 导出 JSON 格式（最近 7 天）
                val jsonResult = AVLEngineStatisticsIntegration.exportStatisticsReport(jsonPath, 7)

                // 导出 CSV 格式
                val manager = AVLEngineStatisticsIntegration.getStatisticsManager()
                var csvResult: String? = null
                if (manager != null) {
                    val endTime = System.currentTimeMillis()
                    val startTime = endTime - (7 * 24 * 60 * 60 * 1000L)
                    csvResult = manager.exportToCsv(startTime, endTime, csvPath, "UTF-8")
                }

                withContext(Dispatchers.Main) {
                    val message = StringBuilder()
                    message.append("📤 统计报告导出完成\n\n")

                    if (jsonResult != null) {
                        message.append("✅ JSON 报告已保存\n")
                    } else {
                        message.append("❌ JSON 报告导出失败\n")
                    }

                    if (csvResult != null) {
                        message.append("✅ CSV 报告已保存\n")
                    } else {
                        message.append("❌ CSV 报告导出失败\n")
                    }

                    message.append("\n文件保存在应用外部存储目录")

                    Toast.makeText(context, message.toString(), Toast.LENGTH_LONG).show()
                }

            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    Toast.makeText(requireContext(), "导出失败：${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    /**
     * 清空缓存和统计数据
     */
    private fun clearCacheAndStatistics() {
        AlertDialog.Builder(requireContext())
            .setTitle("清空数据")
            .setMessage("确定要清空扫描结果缓存和统计数据吗？此操作不可恢复。")
            .setPositiveButton("确定") { _, _ ->
                CoroutineScope(Dispatchers.IO).launch {
                    try {
                        // 清空扫描结果缓存
                        DataManager.getInstance().clearAllData(TABLE_CACHE)

                        // 清空统计数据
                        val statisticsManager = AVLEngineStatisticsIntegration.getStatisticsManager()
                        statisticsManager?.let { manager ->
                            // 清空所有统计记录
                            manager.recordManager?.clearAllRecords()
                        }

                        withContext(Dispatchers.Main) {
                            Toast.makeText(requireContext(), "清空成功", Toast.LENGTH_SHORT).show()
                            Log.i(SettingsFragment::class.java.simpleName, "清空缓存和统计数据成功")
                        }

                    } catch (e: Exception) {
                        withContext(Dispatchers.Main) {
                            Toast.makeText(requireContext(), "清空失败：${e.message}", Toast.LENGTH_SHORT).show()
                            Log.e(SettingsFragment::class.java.simpleName, "清空缓存和统计数据失败", e)
                        }
                    }
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }
}