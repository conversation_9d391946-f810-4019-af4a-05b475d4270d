package com.antiy.avlsdk.entity;

/**
 * 扫描会话摘要
 * 
 * 包含一次完整扫描会话的统计摘要信息。
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ScanSessionSummary {
    
    /** 会话唯一标识符 */
    public String sessionId;
    
    /** 扫描类型 (LOCAL/CLOUD/MIXED) */
    public String scanType;
    
    /** 扫描路径 */
    public String scanPath;
    
    /** 开始时间戳（毫秒） */
    public long startTime;
    
    /** 结束时间戳（毫秒） */
    public long endTime;
    
    /** 总文件数 */
    public int totalFiles;
    
    /** 实际扫描文件数 */
    public int scannedFiles;
    
    /** 发现威胁数 */
    public int threatsFound;
    
    /** 跳过文件数 */
    public int skippedFiles;
    
    /** 总扫描时间（毫秒） */
    public long totalScanTime;
    
    /** 各引擎使用统计 */
    public EngineUsageStats engineStats;
    
    /** 扫描状态 (COMPLETED/FAILED/CANCELLED) */
    public String status;
    
    /** 错误信息（如果有） */
    public String errorMessage;
    
    public ScanSessionSummary() {
        this.engineStats = new EngineUsageStats();
    }
    
    public ScanSessionSummary(String sessionId, String scanType, String scanPath, long startTime) {
        this();
        this.sessionId = sessionId;
        this.scanType = scanType;
        this.scanPath = scanPath;
        this.startTime = startTime;
    }
    
    /**
     * 引擎使用统计
     */
    public static class EngineUsageStats {
        /** 移动引擎使用次数 */
        public int mobileEngineCount = 0;
        
        /** PC引擎使用次数 */
        public int pcEngineCount = 0;
        
        /** 云查引擎使用次数 */
        public int cloudEngineCount = 0;
        
        /** 移动引擎总耗时（毫秒） */
        public long mobileEngineTime = 0;
        
        /** PC引擎总耗时（毫秒） */
        public long pcEngineTime = 0;
        
        /** 云查引擎总耗时（毫秒） */
        public long cloudEngineTime = 0;
        
        /**
         * 记录引擎使用情况
         * 
         * @param engineType 引擎类型 (MOBILE/PC/CLOUD)
         * @param scanTime 扫描耗时（毫秒）
         */
        public void recordEngineUsage(String engineType, long scanTime) {
            if (engineType == null) {
                return;
            }
            
            switch (engineType.toUpperCase()) {
                case "MOBILE":
                    mobileEngineCount++;
                    mobileEngineTime += scanTime;
                    break;
                case "PC":
                    pcEngineCount++;
                    pcEngineTime += scanTime;
                    break;
                case "CLOUD":
                    cloudEngineCount++;
                    cloudEngineTime += scanTime;
                    break;
            }
        }
        
        /**
         * 获取总引擎使用次数
         */
        public int getTotalEngineUsage() {
            return mobileEngineCount + pcEngineCount + cloudEngineCount;
        }
        
        /**
         * 获取总引擎耗时
         */
        public long getTotalEngineTime() {
            return mobileEngineTime + pcEngineTime + cloudEngineTime;
        }
        
        @Override
        public String toString() {
            return "EngineUsageStats{" +
                    "mobile=" + mobileEngineCount + "(" + mobileEngineTime + "ms), " +
                    "pc=" + pcEngineCount + "(" + pcEngineTime + "ms), " +
                    "cloud=" + cloudEngineCount + "(" + cloudEngineTime + "ms)" +
                    '}';
        }
    }
    
    /**
     * 计算扫描耗时
     */
    public long getDuration() {
        return endTime - startTime;
    }
    
    /**
     * 计算扫描成功率
     */
    public double getSuccessRate() {
        if (totalFiles == 0) {
            return 0.0;
        }
        return (double) scannedFiles / totalFiles * 100.0;
    }
    
    /**
     * 计算威胁检出率
     */
    public double getThreatDetectionRate() {
        if (scannedFiles == 0) {
            return 0.0;
        }
        return (double) threatsFound / scannedFiles * 100.0;
    }
    
    @Override
    public String toString() {
        return "ScanSessionSummary{" +
                "sessionId='" + sessionId + '\'' +
                ", scanType='" + scanType + '\'' +
                ", scanPath='" + scanPath + '\'' +
                ", duration=" + getDuration() + "ms" +
                ", totalFiles=" + totalFiles +
                ", scannedFiles=" + scannedFiles +
                ", threatsFound=" + threatsFound +
                ", skippedFiles=" + skippedFiles +
                ", status='" + status + '\'' +
                ", engineStats=" + engineStats +
                '}';
    }
}
