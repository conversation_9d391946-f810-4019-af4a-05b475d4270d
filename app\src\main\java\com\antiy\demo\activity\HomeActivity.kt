package com.antiy.demo.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.config.Config
import com.antiy.avlsdk.entity.ScanFileType
import com.antiy.avlsdk.statistics.integration.AVLEngineStatisticsIntegration
import com.antiy.demo.App
import com.antiy.demo.R
import com.antiy.demo.base.BaseActivity
import com.antiy.demo.databinding.ActivityHomeBinding
import com.antiy.demo.fragment.HomeFragment
import com.antiy.demo.fragment.ScanFragment
import com.antiy.demo.fragment.SettingsFragment
import com.antiy.demo.fragment.VirusDatabaseFragment

class HomeActivity : BaseActivity<ActivityHomeBinding>() {

    // 创建文件管理权限请求启动器
    private lateinit var storagePermissionLauncher: ActivityResultLauncher<Intent>

    override fun getViewBinding(inflater: LayoutInflater): ActivityHomeBinding {
        return ActivityHomeBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityHomeBinding.inflate(layoutInflater)
        setContentView(binding.root)
        handUri()
        // 初始化权限请求启动器
        initActivityResultLauncher()

        // 检查并请求权限
        checkAndRequestPermissions()

        // 初始化统计功能
        initStatistics()

        initViews()
        setupNavigation()
    }

    @SuppressLint("Recycle")
    private fun handUri() {
        if ((intent?.action == Intent.ACTION_VIEW && intent.data != null) ||
            (intent?.action == Intent.ACTION_SEND && intent.type != null)
        ) {
            val contentUri = intent.data
            Log.d("ContentUri", "收到的Uri: $contentUri")
            contentUri?.let {
                val resolver = applicationContext.contentResolver
                val fd = resolver.openFileDescriptor(it, "r")?.fd
                val scanResult = AVLEngine.getInstance().scanWithFd(fd!!)
                Log.e("ContentUri", "使用fd扫描的结果: $scanResult")
            }
        }
    }

    private fun initActivityResultLauncher() {
        // 定义存储权限请求结果处理
        storagePermissionLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                if (Environment.isExternalStorageManager()) {
                    // 获取到权限，初始化引擎并设置UI
                    initAVLEngine()

                } else {
                    Toast.makeText(this, "需要所有文件访问权限才能继续操作", Toast.LENGTH_LONG)
                        .show()
                    finish() // 没有权限则关闭应用
                }
            }
        }
    }

    /**
     * 检查并请求所需权限
     */
    private fun checkAndRequestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (!Environment.isExternalStorageManager()) {
                // 请求MANAGE_EXTERNAL_STORAGE权限
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
                    data = Uri.parse("package:$packageName")
                }
                // 使用Activity Result Launcher启动权限请求
                storagePermissionLauncher.launch(intent)
            } else {
                // 已有权限，直接初始化
                initAVLEngine()
                initViews()
                setupNavigation()
            }
        } else {
            // Android 11以下版本，直接初始化
            initAVLEngine()
            initViews()
            setupNavigation()
        }
    }

    /**
     * 初始化AVLEngine
     */
    private fun initAVLEngine() {
        val app = application as App
        if (!app.isAVLEngineInitialized()) {
            val uuid = app.initializeAVLEngine()
            Toast.makeText(
                this,
                "AVLEngine初始化成功，UUID: ${uuid.substring(0, 8)}...",
                Toast.LENGTH_SHORT
            ).show()
            // 加载引擎配置
            initAvlConfig()
            supportFragmentManager.fragments.apply {
                if (this.isNotEmpty()) {
                    (this[0] as? HomeFragment)?.updateEngineStatus(app.isAVLEngineInitialized())
                }
            }
        }
    }

    /**
     * 初始化AVL配置
     */
    private fun initAvlConfig() {
        val whiteMap = HashMap<String, Boolean>().apply {
            this["604e9ad96558bddacea9be20eca12e632576020c3072eda00423af8140fc59a9"] = false
        }
        val scanList = arrayListOf(
            ScanFileType.JAR,
            ScanFileType.APK,
            ScanFileType.DEX,
            ScanFileType.MP3,
            ScanFileType.MP4,
            ScanFileType.JPG,
            ScanFileType.PNG,
            ScanFileType.GIF,
            ScanFileType.ELF
        )

        val sharedPrefs = getSharedPreferences("cloud_scan_settings", Context.MODE_PRIVATE)
        val cloudThreshold = sharedPrefs.getInt("cloud_threshold", 5000)
        val cloudSizeThresholdMb = sharedPrefs.getInt("cloud_size_threshold", 500)
        val historyCountThreshold = sharedPrefs.getInt("cache_history_count_limit",2000)

        AVLEngine.getInstance().loadConfig(
            Config.Builder()
                .setBlackWhiteList(whiteMap)
                .setScanType(scanList)
                .setHistorySize(historyCountThreshold.toLong())
                .setHistoryTimeout(7 * 24 * 60 * 60 * 1000)
                .setCloudCheckThreshold(cloudThreshold)
                .setCloudSizeThreshold(cloudSizeThresholdMb * 1024L * 1024L)
                .setSilentPerformanceThreshold(1)
                .build()
        )
    }

    /**
     * 初始化统计功能
     */
    private fun initStatistics() {
        val success = AVLEngineStatisticsIntegration.initializeStatistics(this)
        if (success) {
            Log.i("HomeActivity", "扫描统计功能初始化成功")

            // 显示统计功能状态
            AVLEngineStatisticsIntegration.logStatisticsStatus()
        } else {
            Log.e("HomeActivity", "扫描统计功能初始化失败")
        }
    }

    private fun initViews() {
        // 设置ViewPager适配器
        binding.viewPager.adapter = HomePagerAdapter(this)
        // 禁用ViewPager滑动
        binding.viewPager.isUserInputEnabled = false
    }

    private fun setupNavigation() {
        // 底部导航栏选择监听
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.nav_home -> binding.viewPager.currentItem = 0
                R.id.nav_scan -> binding.viewPager.currentItem = 1
//                R.id.nav_protection -> binding.viewPager.currentItem = 2
                R.id.nav_virus_db -> binding.viewPager.currentItem = 2
                R.id.nav_settings -> binding.viewPager.currentItem = 3
            }
            true
        }
    }

    // ViewPager适配器
    private inner class HomePagerAdapter(fa: FragmentActivity) : FragmentStateAdapter(fa) {
        override fun getItemCount(): Int = 4

        override fun createFragment(position: Int): Fragment {
            return when (position) {
                0 -> HomeFragment()
                1 -> ScanFragment()
//                2 -> ProtectionFragment()
                2 -> VirusDatabaseFragment()
                3 -> SettingsFragment()
                else -> HomeFragment()
            }
        }
    }
}