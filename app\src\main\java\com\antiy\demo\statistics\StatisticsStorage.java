package com.antiy.demo.statistics;

import android.util.Log;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 统计数据存储管理器
 * 
 * 负责统计数据的持久化存储，支持JSON格式存储和CSV导出。
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class StatisticsStorage {
    
    private static final String TAG = "StatisticsStorage";
    
    private final StatisticsConfig config;
    private final String storageRootPath;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
    private final SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault());
    
    public StatisticsStorage(StatisticsConfig config) {
        this.config = config;
        this.storageRootPath = config.getStorageRootPath();
        
        // 确保存储目录存在
        initializeStorageDirectories();
    }
    
    /**
     * 初始化存储目录结构
     */
    private void initializeStorageDirectories() {
        try {
            File rootDir = new File(storageRootPath);
            if (!rootDir.exists()) {
                boolean created = rootDir.mkdirs();
                if (created) {
                    Log.i(TAG, "Created storage directory: " + storageRootPath);
                } else {
                    Log.e(TAG, "Failed to create storage directory: " + storageRootPath);
                }
            }
            
            // 创建年月子目录（当前年月）
            String currentYearMonth = new SimpleDateFormat("yyyy/MM", Locale.getDefault()).format(new Date());
            File yearMonthDir = new File(rootDir, currentYearMonth);
            if (!yearMonthDir.exists()) {
                boolean created = yearMonthDir.mkdirs();
                if (created) {
                    Log.d(TAG, "Created year-month directory: " + yearMonthDir.getAbsolutePath());
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error initializing storage directories", e);
            throw new RuntimeException("Failed to initialize storage directories", e);
        }
    }
    
    /**
     * 保存扫描会话记录
     * 
     * @param record 会话记录
     * @return 是否保存成功
     */
    public boolean saveRecord(ScanSessionRecord record) {
        if (record == null) {
            Log.w(TAG, "Cannot save null record");
            return false;
        }
        
        try {
            // 生成文件路径
            String filePath = generateRecordFilePath(record);
            
            // 转换为JSON
            JSONObject jsonRecord = recordToJson(record);
            
            // 写入文件
            writeJsonToFile(jsonRecord, filePath);
            
            Log.d(TAG, "Record saved successfully: " + record.getSessionId());
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Error saving record: " + record.getSessionId(), e);
            return false;
        }
    }
    
    /**
     * 获取最近的扫描记录
     * 
     * @param limit 记录数量限制
     * @return 扫描记录列表
     */
    public List<ScanSessionRecord> getRecentRecords(int limit) {
        List<ScanSessionRecord> records = new ArrayList<>();
        
        try {
            List<File> jsonFiles = findRecentJsonFiles(limit);
            
            for (File file : jsonFiles) {
                try {
                    ScanSessionRecord record = loadRecordFromFile(file);
                    if (record != null) {
                        records.add(record);
                    }
                } catch (Exception e) {
                    Log.w(TAG, "Error loading record from file: " + file.getName(), e);
                }
            }
            
            Log.d(TAG, "Loaded " + records.size() + " recent records");
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting recent records", e);
        }
        
        return records;
    }
    
    /**
     * 获取指定时间范围的扫描记录
     * 
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 扫描记录列表
     */
    public List<ScanSessionRecord> getRecordsByTimeRange(long startTime, long endTime) {
        List<ScanSessionRecord> records = new ArrayList<>();
        
        try {
            List<File> jsonFiles = findJsonFilesInTimeRange(startTime, endTime);
            
            for (File file : jsonFiles) {
                try {
                    ScanSessionRecord record = loadRecordFromFile(file);
                    if (record != null && 
                        record.getStartTime() >= startTime && 
                        record.getStartTime() <= endTime) {
                        records.add(record);
                    }
                } catch (Exception e) {
                    Log.w(TAG, "Error loading record from file: " + file.getName(), e);
                }
            }
            
            Log.d(TAG, "Loaded " + records.size() + " records in time range");
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting records by time range", e);
        }
        
        return records;
    }
    
    /**
     * 导出统计数据到JSON文件
     * 
     * @param filePath 导出文件路径
     * @param days 导出最近几天的数据
     * @return 导出结果描述
     */
    public String exportToJson(String filePath, int days) {
        try {
            long endTime = System.currentTimeMillis();
            long startTime = endTime - (days * 24 * 60 * 60 * 1000L);
            
            List<ScanSessionRecord> records = getRecordsByTimeRange(startTime, endTime);
            
            JSONArray jsonArray = new JSONArray();
            for (ScanSessionRecord record : records) {
                jsonArray.put(recordToJson(record));
            }
            
            JSONObject exportData = new JSONObject();
            exportData.put("exportTime", System.currentTimeMillis());
            exportData.put("exportTimeString", timeFormat.format(new Date()));
            exportData.put("daysCovered", days);
            exportData.put("recordCount", records.size());
            exportData.put("records", jsonArray);
            
            writeJsonToFile(exportData, filePath);
            
            String result = "Exported " + records.size() + " records from last " + days + " days to: " + filePath;
            Log.i(TAG, result);
            return result;
            
        } catch (Exception e) {
            String error = "Export to JSON failed: " + e.getMessage();
            Log.e(TAG, error, e);
            return error;
        }
    }
    
    /**
     * 导出统计数据到CSV文件
     * 
     * @param filePath 导出文件路径
     * @param days 导出最近几天的数据
     * @return 导出结果描述
     */
    public String exportToCsv(String filePath, int days) {
        try {
            long endTime = System.currentTimeMillis();
            long startTime = endTime - (days * 24 * 60 * 60 * 1000L);
            
            List<ScanSessionRecord> records = getRecordsByTimeRange(startTime, endTime);
            
            StringBuilder csv = new StringBuilder();
            
            // CSV头部
            csv.append("SessionId,ScanType,ScanPath,StartTime,EndTime,Duration,")
               .append("TotalFiles,ScannedFiles,ThreatsFound,SkippedFiles,")
               .append("Status,SuccessRate,ThreatDetectionRate,AvgScanTime,")
               .append("MobileEngineCount,PCEngineCount,CloudEngineCount\n");
            
            // 数据行
            for (ScanSessionRecord record : records) {
                csv.append(recordToCsvRow(record)).append("\n");
            }
            
            // 写入文件
            try (FileWriter writer = new FileWriter(filePath)) {
                writer.write(csv.toString());
            }
            
            String result = "Exported " + records.size() + " records from last " + days + " days to CSV: " + filePath;
            Log.i(TAG, result);
            return result;
            
        } catch (Exception e) {
            String error = "Export to CSV failed: " + e.getMessage();
            Log.e(TAG, error, e);
            return error;
        }
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 生成记录文件路径
     */
    private String generateRecordFilePath(ScanSessionRecord record) {
        Date startDate = new Date(record.getStartTime());
        String yearMonth = new SimpleDateFormat("yyyy/MM", Locale.getDefault()).format(startDate);
        String fileName = "scan_" + timeFormat.format(startDate) + "_" + 
                         record.getSessionId().substring(record.getSessionId().length() - 8) + ".json";
        
        File yearMonthDir = new File(storageRootPath, yearMonth);
        if (!yearMonthDir.exists()) {
            yearMonthDir.mkdirs();
        }
        
        return new File(yearMonthDir, fileName).getAbsolutePath();
    }
    
    /**
     * 将记录转换为JSON对象
     */
    private JSONObject recordToJson(ScanSessionRecord record) throws JSONException {
        JSONObject json = new JSONObject();
        
        json.put("sessionId", record.getSessionId());
        json.put("scanType", record.getScanType());
        json.put("scanPath", record.getScanPath());
        json.put("startTime", record.getStartTime());
        json.put("endTime", record.getEndTime());
        json.put("totalFiles", record.getTotalFiles());
        json.put("scannedFiles", record.getScannedFiles());
        json.put("threatsFound", record.getThreatsFound());
        json.put("skippedFiles", record.getSkippedFiles());
        json.put("totalScanTime", record.getTotalScanTime());
        json.put("status", record.getStatus());
        json.put("errorMessage", record.getErrorMessage());
        
        // 引擎统计
        JSONObject engineStats = new JSONObject();
        ScanSessionRecord.EngineUsageStats stats = record.getEngineStats();
        engineStats.put("mobileEngineCount", stats.mobileEngineCount);
        engineStats.put("pcEngineCount", stats.pcEngineCount);
        engineStats.put("cloudEngineCount", stats.cloudEngineCount);
        engineStats.put("mobileEngineTime", stats.mobileEngineTime);
        engineStats.put("pcEngineTime", stats.pcEngineTime);
        engineStats.put("cloudEngineTime", stats.cloudEngineTime);
        json.put("engineStats", engineStats);
        
        // 威胁信息
        JSONArray threats = new JSONArray();
        for (ScanSessionRecord.ThreatInfo threat : record.getThreats()) {
            JSONObject threatJson = new JSONObject();
            threatJson.put("filePath", threat.filePath);
            threatJson.put("virusName", threat.virusName);
            threatJson.put("engineType", threat.engineType);
            threatJson.put("scanTime", threat.scanTime);
            threatJson.put("timestamp", threat.timestamp);
            threats.put(threatJson);
        }
        json.put("threats", threats);
        
        return json;
    }
    
    /**
     * 将JSON写入文件
     */
    private void writeJsonToFile(JSONObject json, String filePath) throws IOException {
        try (FileWriter writer = new FileWriter(filePath)) {
            writer.write(json.toString(2)); // 格式化输出
        }
    }
    
    /**
     * 查找最近的JSON文件
     */
    private List<File> findRecentJsonFiles(int limit) {
        List<File> jsonFiles = new ArrayList<>();
        
        File rootDir = new File(storageRootPath);
        findJsonFilesRecursively(rootDir, jsonFiles);
        
        // 按修改时间排序（最新的在前）
        jsonFiles.sort((f1, f2) -> Long.compare(f2.lastModified(), f1.lastModified()));
        
        // 限制数量
        if (jsonFiles.size() > limit) {
            jsonFiles = jsonFiles.subList(0, limit);
        }
        
        return jsonFiles;
    }
    
    /**
     * 查找指定时间范围内的JSON文件
     */
    private List<File> findJsonFilesInTimeRange(long startTime, long endTime) {
        List<File> jsonFiles = new ArrayList<>();
        
        File rootDir = new File(storageRootPath);
        findJsonFilesRecursively(rootDir, jsonFiles);
        
        // 过滤时间范围
        jsonFiles.removeIf(file -> {
            long fileTime = file.lastModified();
            return fileTime < startTime || fileTime > endTime;
        });
        
        return jsonFiles;
    }
    
    /**
     * 递归查找JSON文件
     */
    private void findJsonFilesRecursively(File dir, List<File> jsonFiles) {
        if (!dir.exists() || !dir.isDirectory()) {
            return;
        }
        
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    findJsonFilesRecursively(file, jsonFiles);
                } else if (file.getName().endsWith(".json") && file.getName().startsWith("scan_")) {
                    jsonFiles.add(file);
                }
            }
        }
    }
    
    /**
     * 从文件加载记录
     */
    private ScanSessionRecord loadRecordFromFile(File file) throws Exception {
        String content = new String(Files.readAllBytes(Paths.get(file.getAbsolutePath())));
        JSONObject json = new JSONObject(content);
        
        // 这里需要实现JSON到ScanSessionRecord的转换
        // 为了简化，暂时返回null，实际实现中需要完整的反序列化逻辑
        return jsonToRecord(json);
    }
    
    /**
     * 将JSON转换为记录对象（简化实现）
     */
    private ScanSessionRecord jsonToRecord(JSONObject json) throws JSONException {
        ScanSessionRecord record = new ScanSessionRecord();
        
        record.setSessionId(json.getString("sessionId"));
        record.setScanType(json.getString("scanType"));
        record.setScanPath(json.getString("scanPath"));
        record.setStartTime(json.getLong("startTime"));
        record.setEndTime(json.getLong("endTime"));
        record.setTotalFiles(json.getInt("totalFiles"));
        record.setScannedFiles(json.getInt("scannedFiles"));
        record.setThreatsFound(json.getInt("threatsFound"));
        record.setSkippedFiles(json.getInt("skippedFiles"));
        record.setTotalScanTime(json.getLong("totalScanTime"));
        record.setStatus(json.getString("status"));
        record.setErrorMessage(json.optString("errorMessage", null));
        
        return record;
    }
    
    /**
     * 将记录转换为CSV行
     */
    private String recordToCsvRow(ScanSessionRecord record) {
        return String.format("%s,%s,%s,%d,%d,%d,%d,%d,%d,%d,%s,%.2f,%.2f,%.2f,%d,%d,%d",
            record.getSessionId(),
            record.getScanType(),
            record.getScanPath().replace(",", ";"), // 避免CSV分隔符冲突
            record.getStartTime(),
            record.getEndTime(),
            record.getDuration(),
            record.getTotalFiles(),
            record.getScannedFiles(),
            record.getThreatsFound(),
            record.getSkippedFiles(),
            record.getStatus(),
            record.getSuccessRate(),
            record.getThreatDetectionRate(),
            record.getAverageScanTime(),
            record.getEngineStats().mobileEngineCount,
            record.getEngineStats().pcEngineCount,
            record.getEngineStats().cloudEngineCount
        );
    }
}
