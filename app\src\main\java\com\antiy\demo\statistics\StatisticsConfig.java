package com.antiy.demo.statistics;

import android.content.Context;

/**
 * 统计功能配置类
 * 
 * 用于配置统计功能的各种参数，包括存储路径、保留策略、性能监控等。
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class StatisticsConfig {
    
    /** 存储根目录 */
    private String storageRootPath;
    
    /** 记录保留天数 */
    private int retentionDays = 30;
    
    /** 单日最大记录数 */
    private int maxRecordsPerDay = 100;
    
    /** 是否启用性能监控 */
    private boolean enablePerformanceMonitoring = true;
    
    /** 是否启用详细日志 */
    private boolean enableDetailedLogging = false;
    
    /** 是否启用云查统计 */
    private boolean enableCloudStatistics = true;
    
    /** 是否启用引擎统计 */
    private boolean enableEngineStatistics = true;
    
    /** 默认导出格式 */
    private String defaultExportFormat = "JSON";
    
    /** 自动清理过期数据 */
    private boolean autoCleanup = true;
    
    /** 性能采样间隔(毫秒) */
    private int performanceSampleInterval = 1000;
    
    /** 最大并发会话数 */
    private int maxConcurrentSessions = 5;
    
    private StatisticsConfig() {
        // 私有构造函数，强制使用Builder模式
    }
    
    // Getter方法
    public String getStorageRootPath() { return storageRootPath; }
    public int getRetentionDays() { return retentionDays; }
    public int getMaxRecordsPerDay() { return maxRecordsPerDay; }
    public boolean isEnablePerformanceMonitoring() { return enablePerformanceMonitoring; }
    public boolean isEnableDetailedLogging() { return enableDetailedLogging; }
    public boolean isEnableCloudStatistics() { return enableCloudStatistics; }
    public boolean isEnableEngineStatistics() { return enableEngineStatistics; }
    public String getDefaultExportFormat() { return defaultExportFormat; }
    public boolean isAutoCleanup() { return autoCleanup; }
    public int getPerformanceSampleInterval() { return performanceSampleInterval; }
    public int getMaxConcurrentSessions() { return maxConcurrentSessions; }
    
    /**
     * Builder模式构建器
     */
    public static class Builder {
        private StatisticsConfig config = new StatisticsConfig();
        
        public Builder setStorageRootPath(String path) {
            config.storageRootPath = path;
            return this;
        }
        
        public Builder setRetentionDays(int days) {
            if (days > 0) {
                config.retentionDays = days;
            }
            return this;
        }
        
        public Builder setMaxRecordsPerDay(int maxRecords) {
            if (maxRecords > 0) {
                config.maxRecordsPerDay = maxRecords;
            }
            return this;
        }
        
        public Builder setEnablePerformanceMonitoring(boolean enable) {
            config.enablePerformanceMonitoring = enable;
            return this;
        }
        
        public Builder setEnableDetailedLogging(boolean enable) {
            config.enableDetailedLogging = enable;
            return this;
        }
        
        public Builder setEnableCloudStatistics(boolean enable) {
            config.enableCloudStatistics = enable;
            return this;
        }
        
        public Builder setEnableEngineStatistics(boolean enable) {
            config.enableEngineStatistics = enable;
            return this;
        }
        
        public Builder setDefaultExportFormat(String format) {
            if (format != null && (format.equals("JSON") || format.equals("CSV"))) {
                config.defaultExportFormat = format;
            }
            return this;
        }
        
        public Builder setAutoCleanup(boolean autoCleanup) {
            config.autoCleanup = autoCleanup;
            return this;
        }
        
        public Builder setPerformanceSampleInterval(int interval) {
            if (interval > 0) {
                config.performanceSampleInterval = interval;
            }
            return this;
        }
        
        public Builder setMaxConcurrentSessions(int maxSessions) {
            if (maxSessions > 0) {
                config.maxConcurrentSessions = maxSessions;
            }
            return this;
        }
        
        public StatisticsConfig build() {
            if (config.storageRootPath == null || config.storageRootPath.trim().isEmpty()) {
                throw new IllegalStateException("Storage root path must be set");
            }
            return config;
        }
    }
    
    /**
     * 获取默认配置
     * 
     * @param context 应用上下文
     * @return 默认配置对象
     */
    public static StatisticsConfig getDefault(Context context) {
        return new Builder()
            .setStorageRootPath(context.getExternalFilesDir("scan_statistics").getAbsolutePath())
            .setRetentionDays(30)
            .setMaxRecordsPerDay(100)
            .setEnablePerformanceMonitoring(true)
            .setEnableDetailedLogging(false)
            .setEnableCloudStatistics(true)
            .setEnableEngineStatistics(true)
            .setDefaultExportFormat("JSON")
            .setAutoCleanup(true)
            .setPerformanceSampleInterval(1000)
            .setMaxConcurrentSessions(5)
            .build();
    }
    
    /**
     * 获取开发环境配置
     * 
     * @param context 应用上下文
     * @return 开发环境配置对象
     */
    public static StatisticsConfig getDevelopmentConfig(Context context) {
        return new Builder()
            .setStorageRootPath(context.getExternalFilesDir("dev_scan_statistics").getAbsolutePath())
            .setRetentionDays(7)
            .setMaxRecordsPerDay(200)
            .setEnablePerformanceMonitoring(true)
            .setEnableDetailedLogging(true)
            .setEnableCloudStatistics(true)
            .setEnableEngineStatistics(true)
            .setDefaultExportFormat("JSON")
            .setAutoCleanup(true)
            .setPerformanceSampleInterval(500)
            .setMaxConcurrentSessions(3)
            .build();
    }
    
    /**
     * 获取轻量级配置
     * 
     * @param context 应用上下文
     * @return 轻量级配置对象
     */
    public static StatisticsConfig getLightweightConfig(Context context) {
        return new Builder()
            .setStorageRootPath(context.getExternalFilesDir("light_scan_statistics").getAbsolutePath())
            .setRetentionDays(15)
            .setMaxRecordsPerDay(50)
            .setEnablePerformanceMonitoring(false)
            .setEnableDetailedLogging(false)
            .setEnableCloudStatistics(false)
            .setEnableEngineStatistics(true)
            .setDefaultExportFormat("JSON")
            .setAutoCleanup(true)
            .setPerformanceSampleInterval(2000)
            .setMaxConcurrentSessions(2)
            .build();
    }
    
    @Override
    public String toString() {
        return "StatisticsConfig{" +
                "storageRootPath='" + storageRootPath + '\'' +
                ", retentionDays=" + retentionDays +
                ", maxRecordsPerDay=" + maxRecordsPerDay +
                ", enablePerformanceMonitoring=" + enablePerformanceMonitoring +
                ", enableDetailedLogging=" + enableDetailedLogging +
                ", enableCloudStatistics=" + enableCloudStatistics +
                ", enableEngineStatistics=" + enableEngineStatistics +
                ", defaultExportFormat='" + defaultExportFormat + '\'' +
                ", autoCleanup=" + autoCleanup +
                ", performanceSampleInterval=" + performanceSampleInterval +
                ", maxConcurrentSessions=" + maxConcurrentSessions +
                '}';
    }
}
