<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="扫描"
            android:textColor="#000000"
            android:textSize="24sp"
            android:textStyle="bold" />

        <!-- 扫描类型选择标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="选择扫描类型"
            android:textColor="#000000"
            android:textSize="16sp" />

        <!-- 扫描类型选择卡片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp">

            <!-- 快速扫描 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardQuickScan"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                android:foreground="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="16dp"
                        android:src="@drawable/ic_quick_scan"
                        app:tint="#4285F4" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:text="快速扫描"
                        android:textColor="#000000"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="扫描常用应用和系统文件"
                        android:textColor="#757575"
                        android:textSize="12sp"
                        android:gravity="center" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="约1-2分钟"
                        android:textColor="#757575"
                        android:textSize="12sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 全面扫描 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardFullScan"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                android:foreground="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="16dp"
                        android:src="@drawable/ic_full_scan"
                        app:tint="#4285F4" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:text="全面扫描"
                        android:textColor="#000000"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="扫描设备所有文件"
                        android:textColor="#757575"
                        android:textSize="12sp"
                        android:gravity="center" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="约5-10分钟"
                        android:textColor="#757575"
                        android:textSize="12sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp">

            <!-- 自定义扫描 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardCustomScan"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                android:foreground="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="16dp"
                        android:src="@drawable/ic_custom_scan"
                        app:tint="#4285F4" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:text="自定义扫描"
                        android:textColor="#000000"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="选择特定文件或文件夹"
                        android:textColor="#757575"
                        android:textSize="12sp"
                        android:gravity="center" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="时间因选择而异"
                        android:textColor="#757575"
                        android:textSize="12sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 定时扫描 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardScheduledScan"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                android:foreground="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="16dp"
                        android:src="@drawable/ic_scheduled_scan"
                        app:tint="#4285F4" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:text="定时扫描"
                        android:textColor="#000000"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="设置自动扫描计划"
                        android:textColor="#757575"
                        android:textSize="12sp"
                        android:gravity="center" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="按计划执行"
                        android:textColor="#757575"
                        android:textSize="12sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

        <!-- 文件扫描功能区域 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="文件扫描"
                    android:textColor="#000000"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="选择任意文件进行病毒扫描"
                    android:textColor="#757575"
                    android:textSize="12sp"
                    android:layout_marginTop="4dp"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSelectFileScan"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:text="选择文件并扫描"
                    android:icon="@drawable/ic_file"
                    android:layout_marginTop="12dp"/>

                <!-- 选中文件名展示 -->
                <TextView
                    android:id="@+id/textSelectedFile"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textColor="#000000"
                    android:textSize="14sp"
                    android:layout_marginTop="8dp"
                    android:visibility="gone"/>

                <!-- 扫描结果展示 -->
                <TextView
                    android:id="@+id/textFileScanResult"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textColor="#000000"
                    android:textSize="14sp"
                    android:layout_marginTop="8dp"
                    android:visibility="gone"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 高级选项 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:visibility="gone"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="高级选项"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <!-- 云查杀 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="16dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="云查杀"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="使用云端数据库增强检测能力"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <CheckBox
                        android:id="@+id/checkBoxCloudScan"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true" />
                </LinearLayout>

                <!-- 启发式扫描 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="16dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="启发式扫描"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="检测未知威胁和可疑行为"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <CheckBox
                        android:id="@+id/checkBoxBootScan"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true" />
                </LinearLayout>

                <!-- CPU使用限制 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="16dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="CPU使用限制"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="限制扫描时的资源占用"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <Spinner
                        android:id="@+id/spinnerCpuLimit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="120dp"
                        android:background="@drawable/spinner_background" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 开始扫描按钮 -->
        <Button
            android:id="@+id/buttonStartScan"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/rounded_button_background"
            android:text="开始扫描"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:drawableStart="@drawable/ic_play"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:drawablePadding="8dp"
            android:gravity="center" />



        <!-- 最近扫描 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="最近扫描"
            android:textColor="#000000"
            android:visibility="gone"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 最近扫描记录卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:visibility="gone"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 快速扫描记录 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="快速扫描"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="今天 10:30"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="end">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="安全"
                            android:textColor="#4CAF50"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="扫描 156 个文件"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F0F0F0" />

                <!-- 全面扫描记录 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="全面扫描"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="昨天 15:45"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="end">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="安全"
                            android:textColor="#4CAF50"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="扫描 2,345 个文件"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F0F0F0" />

                <!-- 自定义扫描记录 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="自定义扫描"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="3天前 09:12"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="end">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="已处理"
                            android:textColor="#FFA000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="发现 1 个威胁"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</ScrollView>