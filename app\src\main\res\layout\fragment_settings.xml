<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingBottom="24dp">

        <!-- 标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:text="设置"
            android:textColor="#000000"
            android:textSize="24sp"
            android:textStyle="bold" />

        <!-- 用户信息卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:visibility="gone"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:id="@+id/layoutUserProfile"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="@drawable/circle_background_light_blue"
                    android:padding="12dp"
                    android:src="@drawable/ic_person"
                    app:tint="#4285F4" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="用户名"
                        android:textColor="#000000"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="免费用户"
                        android:textColor="#757575"
                        android:textSize="14sp" />
                </LinearLayout>

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_arrow_right"
                    app:tint="#CCCCCC" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 常规设置标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:text="常规设置"
            android:visibility="gone"
            android:textColor="#000000"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 常规设置卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:visibility="gone"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 通知设置 -->
                <LinearLayout
                    android:id="@+id/layoutNotification"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="8dp"
                        android:src="@drawable/ic_notification"
                        app:tint="#4285F4" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="通知设置"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="管理应用通知和提醒"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="#CCCCCC" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginStart="56dp"
                    android:background="#F0F0F0" />

                <!-- 深色模式 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="8dp"
                        android:src="@drawable/ic_dark_mode"
                        app:tint="#4285F4" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="深色模式"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="切换应用显示主题"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <Switch
                        android:id="@+id/switchDarkMode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginStart="56dp"
                    android:background="#F0F0F0" />

                <!-- 语言 -->
                <LinearLayout
                    android:id="@+id/layoutLanguage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="8dp"
                        android:src="@drawable/ic_language"
                        app:tint="#4285F4" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="语言"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="简体中文"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="#CCCCCC" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 高级设置标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:text="高级设置"
            android:textColor="#000000"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 高级设置卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 杀毒引擎设置 -->
                <LinearLayout
                    android:id="@+id/layoutAntivirusEngine"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="8dp"
                        android:src="@drawable/ic_antivirus_engine"
                        app:tint="#4285F4" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="杀毒引擎设置"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="配置杀毒引擎的高级选项"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="#CCCCCC" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginStart="56dp"
                    android:background="#F0F0F0" />

                <!-- 云查杀设置 -->
                <LinearLayout
                    android:id="@+id/layoutCloudScan"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="8dp"
                        android:src="@drawable/ic_cloud"
                        app:tint="#4285F4" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="云查杀设置"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="配置云端查杀功能"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="#CCCCCC" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginStart="56dp"
                    android:background="#F0F0F0" />

                <!-- 性能设置 -->
                <LinearLayout
                    android:id="@+id/layoutPerformance"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="8dp"
                        android:src="@drawable/ic_performance_settings"
                        app:tint="#4285F4" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="性能设置"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="调整CPU和内存使用"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="#CCCCCC" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 关于 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:id="@+id/layoutAbout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/circle_background_light_blue"
                    android:padding="8dp"
                    android:src="@drawable/ic_info"
                    app:tint="#4285F4" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="关于"
                        android:textColor="#000000"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="版本信息和法律声明"
                        android:textColor="#757575"
                        android:textSize="14sp" />
                </LinearLayout>

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_arrow_right"
                    app:tint="#CCCCCC" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 帮助与反馈 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:id="@+id/layoutHelp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/circle_background_light_blue"
                    android:padding="8dp"
                    android:src="@drawable/ic_help"
                    app:tint="#4285F4" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="帮助与反馈"
                        android:textColor="#000000"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="获取支持和提交反馈"
                        android:textColor="#757575"
                        android:textSize="14sp" />
                </LinearLayout>

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_arrow_right"
                    app:tint="#CCCCCC" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>
        <!-- 查看统计 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">
            <LinearLayout
                android:id="@+id/view_statistics_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/circle_background_light_blue"
                    android:padding="8dp"
                    android:src="@drawable/ic_help"
                    app:tint="#4CAF50" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="查看统计"
                        android:textColor="#000000"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="查看扫描统计信息"
                        android:textColor="#757575"
                        android:textSize="14sp" />
                </LinearLayout>

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_arrow_right"
                    app:tint="#CCCCCC" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 导出统计 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">
            <LinearLayout
                android:id="@+id/export_statistics_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/circle_background_light_blue"
                    android:padding="8dp"
                    android:src="@drawable/ic_help"
                    app:tint="#FF9800" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="导出统计"
                        android:textColor="#000000"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="导出扫描统计报告"
                        android:textColor="#757575"
                        android:textSize="14sp" />
                </LinearLayout>

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_arrow_right"
                    app:tint="#CCCCCC" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 清空缓存 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">
            <LinearLayout
                android:id="@+id/clear_cache_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/circle_background_light_blue"
                    android:padding="8dp"
                    android:src="@drawable/ic_help"
                    app:tint="#4285F4" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="清空缓存"
                        android:textColor="#000000"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="清空扫描结果缓存和统计数据"
                        android:textColor="#757575"
                        android:textSize="14sp" />
                </LinearLayout>

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_arrow_right"
                    app:tint="#CCCCCC" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 退出登录 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:visibility="gone"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:id="@+id/layoutLogout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/circle_background_light_red"
                    android:padding="8dp"
                    android:src="@drawable/ic_logout"
                    app:tint="#FF5252" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:text="退出登录"
                    android:textColor="#FF5252"
                    android:textSize="16sp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</ScrollView> 