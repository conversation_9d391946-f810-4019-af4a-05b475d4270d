package com.antiy.demo.statistics;

import java.util.ArrayList;
import java.util.List;

/**
 * 扫描会话记录
 * 
 * 包含一次完整扫描会话的所有统计信息。
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ScanSessionRecord {
    
    /** 会话唯一标识符 */
    private String sessionId;
    
    /** 扫描类型 (LOCAL/CLOUD/MIXED) */
    private String scanType;
    
    /** 扫描路径 */
    private String scanPath;
    
    /** 开始时间戳（毫秒） */
    private long startTime;
    
    /** 结束时间戳（毫秒） */
    private long endTime;
    
    /** 总文件数 */
    private int totalFiles;
    
    /** 实际扫描文件数 */
    private int scannedFiles;
    
    /** 发现威胁数 */
    private int threatsFound;
    
    /** 跳过文件数 */
    private int skippedFiles;
    
    /** 总扫描时间（毫秒） */
    private long totalScanTime;
    
    /** 扫描状态 (COMPLETED/FAILED/CANCELLED) */
    private String status;
    
    /** 错误信息（如果有） */
    private String errorMessage;
    
    /** 引擎使用统计 */
    private EngineUsageStats engineStats;
    
    /** 威胁详情列表 */
    private List<ThreatInfo> threats;
    
    /** 性能指标 */
    private PerformanceMetrics performanceMetrics;
    
    public ScanSessionRecord() {
        this.engineStats = new EngineUsageStats();
        this.threats = new ArrayList<>();
        this.performanceMetrics = new PerformanceMetrics();
    }
    
    public ScanSessionRecord(String sessionId, String scanType, String scanPath, long startTime) {
        this();
        this.sessionId = sessionId;
        this.scanType = scanType;
        this.scanPath = scanPath;
        this.startTime = startTime;
    }
    
    // ==================== Getter和Setter方法 ====================
    
    public String getSessionId() { return sessionId; }
    public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    
    public String getScanType() { return scanType; }
    public void setScanType(String scanType) { this.scanType = scanType; }
    
    public String getScanPath() { return scanPath; }
    public void setScanPath(String scanPath) { this.scanPath = scanPath; }
    
    public long getStartTime() { return startTime; }
    public void setStartTime(long startTime) { this.startTime = startTime; }
    
    public long getEndTime() { return endTime; }
    public void setEndTime(long endTime) { this.endTime = endTime; }
    
    public int getTotalFiles() { return totalFiles; }
    public void setTotalFiles(int totalFiles) { this.totalFiles = totalFiles; }
    
    public int getScannedFiles() { return scannedFiles; }
    public void setScannedFiles(int scannedFiles) { this.scannedFiles = scannedFiles; }
    
    public int getThreatsFound() { return threatsFound; }
    public void setThreatsFound(int threatsFound) { this.threatsFound = threatsFound; }
    
    public int getSkippedFiles() { return skippedFiles; }
    public void setSkippedFiles(int skippedFiles) { this.skippedFiles = skippedFiles; }
    
    public long getTotalScanTime() { return totalScanTime; }
    public void setTotalScanTime(long totalScanTime) { this.totalScanTime = totalScanTime; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    
    public EngineUsageStats getEngineStats() { return engineStats; }
    public void setEngineStats(EngineUsageStats engineStats) { this.engineStats = engineStats; }
    
    public List<ThreatInfo> getThreats() { return threats; }
    public void setThreats(List<ThreatInfo> threats) { this.threats = threats; }
    
    public PerformanceMetrics getPerformanceMetrics() { return performanceMetrics; }
    public void setPerformanceMetrics(PerformanceMetrics performanceMetrics) { this.performanceMetrics = performanceMetrics; }
    
    // ==================== 计算方法 ====================
    
    /**
     * 计算扫描耗时
     */
    public long getDuration() {
        return endTime - startTime;
    }
    
    /**
     * 计算扫描成功率
     */
    public double getSuccessRate() {
        if (totalFiles == 0) {
            return 0.0;
        }
        return (double) scannedFiles / totalFiles * 100.0;
    }
    
    /**
     * 计算威胁检出率
     */
    public double getThreatDetectionRate() {
        if (scannedFiles == 0) {
            return 0.0;
        }
        return (double) threatsFound / scannedFiles * 100.0;
    }
    
    /**
     * 计算平均扫描时间
     */
    public double getAverageScanTime() {
        if (scannedFiles == 0) {
            return 0.0;
        }
        return (double) totalScanTime / scannedFiles;
    }
    
    /**
     * 添加威胁信息
     */
    public void addThreat(ThreatInfo threat) {
        if (threat != null) {
            threats.add(threat);
            threatsFound = threats.size();
        }
    }
    
    /**
     * 检查是否成功完成
     */
    public boolean isSuccessful() {
        return "COMPLETED".equals(status);
    }
    
    /**
     * 检查是否有威胁
     */
    public boolean hasThreats() {
        return threatsFound > 0;
    }
    
    @Override
    public String toString() {
        return "ScanSessionRecord{" +
                "sessionId='" + sessionId + '\'' +
                ", scanType='" + scanType + '\'' +
                ", scanPath='" + scanPath + '\'' +
                ", duration=" + getDuration() + "ms" +
                ", totalFiles=" + totalFiles +
                ", scannedFiles=" + scannedFiles +
                ", threatsFound=" + threatsFound +
                ", skippedFiles=" + skippedFiles +
                ", status='" + status + '\'' +
                ", successRate=" + String.format("%.1f", getSuccessRate()) + "%" +
                ", avgScanTime=" + String.format("%.1f", getAverageScanTime()) + "ms" +
                '}';
    }
    
    // ==================== 内部类 ====================
    
    /**
     * 引擎使用统计
     */
    public static class EngineUsageStats {
        public int mobileEngineCount = 0;
        public int pcEngineCount = 0;
        public int cloudEngineCount = 0;
        public long mobileEngineTime = 0;
        public long pcEngineTime = 0;
        public long cloudEngineTime = 0;
        
        public void recordEngineUsage(String engineType, long scanTime) {
            if (engineType == null) return;
            
            switch (engineType.toUpperCase()) {
                case "MOBILE":
                    mobileEngineCount++;
                    mobileEngineTime += scanTime;
                    break;
                case "PC":
                    pcEngineCount++;
                    pcEngineTime += scanTime;
                    break;
                case "CLOUD":
                    cloudEngineCount++;
                    cloudEngineTime += scanTime;
                    break;
            }
        }
        
        public int getTotalEngineUsage() {
            return mobileEngineCount + pcEngineCount + cloudEngineCount;
        }
        
        public long getTotalEngineTime() {
            return mobileEngineTime + pcEngineTime + cloudEngineTime;
        }
    }
    
    /**
     * 威胁信息
     */
    public static class ThreatInfo {
        public String filePath;
        public String virusName;
        public String engineType;
        public long scanTime;
        public long timestamp;
        
        public ThreatInfo(String filePath, String virusName, String engineType, long scanTime) {
            this.filePath = filePath;
            this.virusName = virusName;
            this.engineType = engineType;
            this.scanTime = scanTime;
            this.timestamp = System.currentTimeMillis();
        }
    }
    
    /**
     * 性能指标
     */
    public static class PerformanceMetrics {
        public long maxMemoryUsed = 0;
        public double avgCpuUsage = 0.0;
        public int ioOperations = 0;
        public long networkBytes = 0;
        
        // 可以根据需要添加更多性能指标
    }
}
