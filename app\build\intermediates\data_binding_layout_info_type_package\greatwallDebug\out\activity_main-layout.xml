<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.antiy.demo" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="158" endOffset="51"/></Target><Target id="@+id/et_scan_path" view="EditText"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="9"/></Target><Target id="@+id/btn_scan" view="Button"><Expressions/><location startLine="20" startOffset="4" endLine="28" endOffset="9"/></Target><Target id="@+id/btn_scan_pause" view="Button"><Expressions/><location startLine="30" startOffset="4" endLine="39" endOffset="9"/></Target><Target id="@+id/btn_scan_resume" view="Button"><Expressions/><location startLine="41" startOffset="4" endLine="50" endOffset="9"/></Target><Target id="@+id/btn_scan_stop" view="Button"><Expressions/><location startLine="52" startOffset="4" endLine="61" endOffset="9"/></Target><Target id="@+id/btn_update_virus" view="Button"><Expressions/><location startLine="63" startOffset="4" endLine="71" endOffset="9"/></Target><Target id="@+id/btn_scan_24_hours" view="Button"><Expressions/><location startLine="73" startOffset="4" endLine="81" endOffset="9"/></Target><Target id="@+id/btn_hand_update_virus" view="Button"><Expressions/><location startLine="83" startOffset="4" endLine="92" endOffset="9"/></Target><Target id="@+id/btn_uuid" view="Button"><Expressions/><location startLine="94" startOffset="4" endLine="102" endOffset="9"/></Target><Target id="@+id/btn_concurrent_scan" view="Button"><Expressions/><location startLine="103" startOffset="4" endLine="111" endOffset="9"/></Target><Target id="@+id/btn_silent_scan" view="Button"><Expressions/><location startLine="112" startOffset="4" endLine="120" endOffset="9"/></Target><Target id="@+id/btn_cpu_pause" view="Button"><Expressions/><location startLine="121" startOffset="4" endLine="128" endOffset="9"/></Target><Target id="@+id/btn_cpu_continue" view="Button"><Expressions/><location startLine="130" startOffset="4" endLine="138" endOffset="9"/></Target><Target id="@+id/btn_clear_cache" view="Button"><Expressions/><location startLine="139" startOffset="4" endLine="147" endOffset="9"/></Target><Target id="@+id/rv" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="148" startOffset="4" endLine="157" endOffset="9"/></Target></Targets></Layout>