package com.antiy.avlsdk.scan;

import com.antiy.avlsdk.callback.IScanner;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.callback.ScanStatisticsCallback;
import com.antiy.avlsdk.entity.ResultScan;

import java.io.File;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

public abstract class BaseScanner implements IScanner {
    protected static final int BATCH_SIZE = 10;
    protected static final int CORE_POOL_SIZE = 4;
    protected static final int MAX_POOL_SIZE = 8;
    protected static final long KEEP_ALIVE_TIME = 60L;

    protected final List<File> files;
    protected final ScanListener callback;
    protected final BlockingQueue<ScanTask> taskQueue;
    protected final AtomicBoolean isPaused;
    protected final AtomicBoolean isStopped;
    protected final AtomicInteger currentIndex;

    // 统计相关字段
    protected ScanStatisticsCallback statisticsCallback;
    protected String sessionId;

    protected BaseScanner(List<File> files, ScanListener callback) {
        this.files = files;
        this.callback = callback;
        this.taskQueue = new LinkedBlockingQueue<>();
        this.isPaused = new AtomicBoolean(false);
        this.isStopped = new AtomicBoolean(false);
        this.currentIndex = new AtomicInteger(0);
    }

    @Override
    public void pauseScan() {
        isPaused.set(true);
    }

    @Override
    public void resumeScan() {
        isPaused.set(false);
    }

    @Override
    public void stopScan() {
        isStopped.set(true);
    }

    @Override
    public boolean isPaused() {
        return isPaused.get();
    }

    @Override
    public boolean isStopped() {
        return isStopped.get();
    }

    protected void handlePause() {
        if (isPaused.get()) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    protected static class ScanTask {
        public final int index;
        public final File file;

        public ScanTask(int index, File file) {
            this.index = index;
            this.file = file;
        }

        @Override
        public String toString() {
            return "ScanTask{" +
                    "index=" + index +
                    ", file=" + file +
                    '}';
        }
    }

    // ==================== 统计相关方法 ====================

    /**
     * 设置统计信息
     *
     * @param callback 统计回调接口
     * @param sessionId 会话ID
     */
    public void setStatisticsInfo(ScanStatisticsCallback callback, String sessionId) {
        this.statisticsCallback = callback;
        this.sessionId = sessionId;
    }

    /**
     * 通知文件扫描完成（用于统计）
     *
     * @param index 文件索引
     * @param filePath 文件路径
     * @param result 扫描结果
     * @param scanTime 扫描耗时（毫秒）
     * @param engineType 使用的引擎类型 (MOBILE/PC/CLOUD)
     */
    protected void notifyFileScanned(int index, String filePath, ResultScan result,
                                   long scanTime, String engineType) {
        if (statisticsCallback != null && sessionId != null) {
            statisticsCallback.onFileScanned(sessionId, index, filePath,
                                           result, scanTime, engineType);
        }
    }

    /**
     * 通知扫描进度更新（用于统计）
     *
     * @param scannedCount 已扫描文件数
     */
    protected void notifyProgressUpdate(int scannedCount) {
        if (statisticsCallback != null && sessionId != null) {
            statisticsCallback.onScanProgress(sessionId, files.size(), scannedCount);
        }
    }
}
