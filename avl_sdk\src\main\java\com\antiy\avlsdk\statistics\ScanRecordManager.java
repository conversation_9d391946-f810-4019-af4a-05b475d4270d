package com.antiy.avlsdk.statistics;

import android.content.Context;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.statistics.entity.ScanSessionRecord;
import com.antiy.avlsdk.statistics.storage.JsonFileManager;
import com.antiy.avlsdk.statistics.storage.StatisticsDatabase;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 扫描记录管理器
 * 负责扫描记录的存储、读取和管理
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ScanRecordManager {
    
    private static final String TAG = "ScanRecordManager";
    
    /** 单例实例 */
    private static volatile ScanRecordManager instance;
    
    /** 应用上下文 */
    private Context context;
    
    /** JSON文件管理器 */
    private JsonFileManager jsonFileManager;
    
    /** 统计数据库 */
    private StatisticsDatabase statisticsDatabase;
    
    /** 记录存储根目录 */
    private File recordsRootDir;
    
    /** 日期格式化器 */
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy/MM", Locale.getDefault());
    private static final SimpleDateFormat DAY_FORMAT = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
    
    /** 最大记录保存天数 */
    private static final int MAX_RECORD_DAYS = 30;
    
    /** 最大单日记录数 */
    private static final int MAX_RECORDS_PER_DAY = 100;
    
    private ScanRecordManager(Context context) {
        this.context = context.getApplicationContext();
        initialize();
    }
    
    /**
     * 获取单例实例
     */
    public static ScanRecordManager getInstance(Context context) {
        if (instance == null) {
            synchronized (ScanRecordManager.class) {
                if (instance == null) {
                    instance = new ScanRecordManager(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化管理器
     */
    private void initialize() {
        try {
            // 创建记录存储目录
            recordsRootDir = new File(context.getFilesDir(), "scan_records");
            if (!recordsRootDir.exists()) {
                recordsRootDir.mkdirs();
            }
            
            // 初始化JSON文件管理器
            jsonFileManager = new JsonFileManager(recordsRootDir);
            
            // 初始化统计数据库
            statisticsDatabase = new StatisticsDatabase(context);
            
            AVLEngine.Logger.info(TAG + ": Initialized successfully, records dir: " + recordsRootDir.getAbsolutePath());
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to initialize: " + e.getMessage());
        }
    }
    
    /**
     * 保存扫描记录
     */
    public boolean saveRecord(ScanSessionRecord record) {
        if (record == null) {
            AVLEngine.Logger.warn(TAG + ": Cannot save null record");
            return false;
        }
        
        try {
            // 保存JSON文件
            String jsonFilePath = jsonFileManager.saveRecord(record);
            if (jsonFilePath == null) {
                AVLEngine.Logger.error(TAG + ": Failed to save JSON file for record: " + record.scanId);
                return false;
            }
            
            // 保存到数据库索引
            boolean dbResult = statisticsDatabase.saveSessionRecord(record, jsonFilePath);
            if (!dbResult) {
                AVLEngine.Logger.warn(TAG + ": Failed to save database index for record: " + record.scanId);
                // JSON文件已保存，数据库索引失败不影响主要功能
            }
            
            AVLEngine.Logger.info(TAG + ": Successfully saved record: " + record.scanId);
            return true;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to save record " + record.scanId + ": " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 根据扫描ID获取记录
     */
    public ScanSessionRecord getRecord(String scanId) {
        if (scanId == null || scanId.isEmpty()) {
            return null;
        }
        
        try {
            // 先从数据库获取JSON文件路径
            String jsonFilePath = statisticsDatabase.getJsonFilePath(scanId);
            if (jsonFilePath == null) {
                AVLEngine.Logger.warn(TAG + ": No JSON file path found for scan ID: " + scanId);
                return null;
            }
            
            // 从JSON文件读取完整记录
            return jsonFileManager.loadRecord(jsonFilePath);
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get record " + scanId + ": " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取最近的扫描记录
     */
    public List<ScanSessionRecord> getRecentRecords(int limit) {
        try {
            List<String> recentScanIds = statisticsDatabase.getRecentScanIds(limit);
            List<ScanSessionRecord> records = new ArrayList<>();
            
            for (String scanId : recentScanIds) {
                ScanSessionRecord record = getRecord(scanId);
                if (record != null) {
                    records.add(record);
                }
            }
            
            return records;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get recent records: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取指定日期的扫描记录
     */
    public List<ScanSessionRecord> getRecordsByDate(String date) {
        try {
            List<String> scanIds = statisticsDatabase.getScanIdsByDate(date);
            List<ScanSessionRecord> records = new ArrayList<>();
            
            for (String scanId : scanIds) {
                ScanSessionRecord record = getRecord(scanId);
                if (record != null) {
                    records.add(record);
                }
            }
            
            return records;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get records by date " + date + ": " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取指定时间范围的扫描记录
     */
    public List<ScanSessionRecord> getRecordsByTimeRange(long startTime, long endTime) {
        try {
            List<String> scanIds = statisticsDatabase.getScanIdsByTimeRange(startTime, endTime);
            List<ScanSessionRecord> records = new ArrayList<>();
            
            for (String scanId : scanIds) {
                ScanSessionRecord record = getRecord(scanId);
                if (record != null) {
                    records.add(record);
                }
            }
            
            return records;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get records by time range: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 删除指定的扫描记录
     */
    public boolean deleteRecord(String scanId) {
        if (scanId == null || scanId.isEmpty()) {
            return false;
        }
        
        try {
            // 获取JSON文件路径
            String jsonFilePath = statisticsDatabase.getJsonFilePath(scanId);
            
            // 删除数据库记录
            boolean dbResult = statisticsDatabase.deleteSessionRecord(scanId);
            
            // 删除JSON文件
            boolean fileResult = true;
            if (jsonFilePath != null) {
                fileResult = jsonFileManager.deleteRecord(jsonFilePath);
            }
            
            AVLEngine.Logger.info(TAG + ": Deleted record " + scanId + ", db: " + dbResult + ", file: " + fileResult);
            return dbResult && fileResult;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to delete record " + scanId + ": " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 清理过期记录
     */
    public void cleanupExpiredRecords() {
        try {
            long cutoffTime = System.currentTimeMillis() - (MAX_RECORD_DAYS * 24 * 60 * 60 * 1000L);

            // 获取过期的扫描ID
            List<String> expiredScanIds = statisticsDatabase.getExpiredScanIds(cutoffTime);

            AVLEngine.Logger.info(TAG + ": Found " + expiredScanIds.size() + " expired records to cleanup");

            // 删除过期记录
            int deletedCount = 0;
            for (String scanId : expiredScanIds) {
                if (deleteRecord(scanId)) {
                    deletedCount++;
                }
            }

            // 清理空目录
            jsonFileManager.cleanupEmptyDirectories();

            AVLEngine.Logger.info(TAG + ": Cleanup completed, deleted " + deletedCount + " expired records");

        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to cleanup expired records: " + e.getMessage());
        }
    }

    /**
     * 清空所有统计记录
     */
    public boolean clearAllRecords() {
        try {
            AVLEngine.Logger.info(TAG + ": Starting to clear all statistics records");

            // 清空数据库中的所有统计数据
            boolean dbResult = statisticsDatabase.clearAllStatistics();

            // 清空JSON文件目录
            boolean fileResult = jsonFileManager.clearAllRecords();

            AVLEngine.Logger.info(TAG + ": Clear all records completed - db: " + dbResult + ", files: " + fileResult);

            return dbResult && fileResult;

        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to clear all records: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取存储统计信息
     */
    public StorageStats getStorageStats() {
        try {
            StorageStats stats = new StorageStats();
            
            // 获取记录总数
            stats.totalRecords = statisticsDatabase.getTotalRecordCount();
            
            // 计算存储空间使用
            stats.totalSizeBytes = calculateDirectorySize(recordsRootDir);
            
            // 获取最早和最新记录时间
            long[] timeRange = statisticsDatabase.getRecordTimeRange();
            stats.earliestRecordTime = timeRange[0];
            stats.latestRecordTime = timeRange[1];
            
            return stats;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to get storage stats: " + e.getMessage());
            return new StorageStats();
        }
    }
    
    /**
     * 计算目录大小
     */
    private long calculateDirectorySize(File directory) {
        long size = 0;
        if (directory != null && directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        size += calculateDirectorySize(file);
                    } else {
                        size += file.length();
                    }
                }
            }
        }
        return size;
    }
    
    /**
     * 存储统计信息
     */
    public static class StorageStats {
        public int totalRecords = 0;
        public long totalSizeBytes = 0;
        public long earliestRecordTime = 0;
        public long latestRecordTime = 0;
        
        public String getFormattedSize() {
            if (totalSizeBytes < 1024) {
                return totalSizeBytes + " B";
            } else if (totalSizeBytes < 1024 * 1024) {
                return String.format("%.1f KB", totalSizeBytes / 1024.0);
            } else {
                return String.format("%.1f MB", totalSizeBytes / (1024.0 * 1024.0));
            }
        }
        
        @Override
        public String toString() {
            return "StorageStats{" +
                    "records=" + totalRecords +
                    ", size=" + getFormattedSize() +
                    '}';
        }
    }
}
