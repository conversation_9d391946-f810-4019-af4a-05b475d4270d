# 扫描记录统计系统

## 概述

本系统为AVL引擎提供完整的扫描记录统计功能，支持扫描过程中的数据收集、存储、查询和导出。采用JSON格式存储详细数据，SQLite数据库存储索引，解决了CSV格式在Windows下的中文乱码问题。

## 核心特性

### 📊 统计维度
- **基础统计**：总文件数、扫描文件数、威胁数、跳过文件数等
- **引擎统计**：移动引擎、PC引擎、云查引擎的分别统计
- **性能数据**：内存使用、CPU使用率、IO操作次数等
- **威胁信息**：详细的威胁检出记录，包含文件路径、病毒名称、风险级别等
- **时间统计**：扫描耗时、单文件平均扫描时间等

### 💾 存储方案
- **主存储**：JSON格式，支持UTF-8编码，解决中文乱码问题
- **索引存储**：SQLite数据库，提供快速查询能力
- **目录结构**：按年月分目录存储，便于管理
- **自动清理**：支持过期记录自动清理

### 🔧 API接口
- **纯API设计**：不涉及UI，可灵活集成到任何应用中
- **工厂模式**：简化使用，提供便捷的创建和管理方法
- **监听器包装**：无缝集成现有扫描流程
- **多种导出格式**：支持JSON、CSV格式导出

## 快速开始

### 1. 初始化统计功能

```java
// 在应用初始化时调用
boolean success = ScanStatisticsFactory.initialize(context);
if (success) {
    Log.i(TAG, "扫描统计功能初始化成功");
} else {
    Log.e(TAG, "扫描统计功能初始化失败");
}
```

### 2. 集成到扫描流程

```java
// 创建原始扫描监听器
ScanListener originalListener = new ScanListener() {
    @Override
    public void scanStart() {
        // 原有的扫描开始逻辑
    }
    
    @Override
    public void scanFinish() {
        // 原有的扫描完成逻辑
    }
    
    // ... 其他回调方法
};

// 创建统计感知的监听器
ScanListener statisticsListener = ScanStatisticsFactory.createStatisticsAwareListener(
    originalListener, "LOCAL", "/sdcard/scan_path");

// 使用统计监听器进行扫描
AVLEngine.getInstance().scanDir("/sdcard/scan_path", statisticsListener);
```

### 3. 查询统计数据

```java
// 获取统计管理器
ScanStatisticsManager manager = ScanStatisticsFactory.getStatisticsManager();

// 获取最近10次扫描记录
List<ScanSessionRecord> recentScans = manager.getRecentScans(10);

// 获取今日统计
ScanStatisticsManager.DailyStatistics todayStats = manager.getDailyStatistics("2024-12-04");

// 获取指定时间范围的记录
long startTime = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000L); // 7天前
long endTime = System.currentTimeMillis();
List<ScanSessionRecord> weeklyScans = manager.getScansByTimeRange(startTime, endTime);
```

### 4. 导出统计报告

```java
// 导出JSON格式报告
String jsonPath = "/sdcard/scan_report.json";
String result = manager.exportStatisticsReport(startTime, endTime, jsonPath);

// 导出CSV格式报告（UTF-8编码，解决中文乱码）
String csvPath = "/sdcard/scan_report.csv";
String csvResult = manager.exportToCsv(startTime, endTime, csvPath, "UTF-8");
```

## 数据结构

### 扫描会话记录 (ScanSessionRecord)
```java
public class ScanSessionRecord {
    public String scanId;           // 扫描会话ID
    public String scanType;         // 扫描类型：LOCAL/CLOUD/MIXED
    public long startTime;          // 开始时间
    public long endTime;            // 结束时间
    public long duration;           // 耗时
    public String status;           // 状态：COMPLETED/FAILED/CANCELLED
    public ScanStatistics statistics;      // 统计数据
    public EngineStatistics engineStats;   // 引擎统计
    public List<ThreatInfo> threats;       // 威胁列表
    public PerformanceData performance;    // 性能数据
}
```

### 统计数据 (ScanStatistics)
```java
public class ScanStatistics {
    public int totalFiles;          // 总文件数
    public int scannedFiles;        // 扫描文件数
    public int maliciousFiles;      // 恶意文件数
    public int cleanFiles;          // 干净文件数
    public int cacheHitFiles;       // 缓存命中文件数
    public long totalScanTime;      // 总扫描时间
    public double avgScanTimePerFile; // 平均单文件扫描时间
}
```

### 威胁信息 (ThreatInfo)
```java
public class ThreatInfo {
    public String filePath;         // 文件路径
    public String fileName;         // 文件名
    public String virusName;        // 病毒名称
    public String sha256;           // 文件哈希
    public long fileSize;           // 文件大小
    public long detectedTime;       // 检测时间
    public String engine;           // 检测引擎
    public String riskLevel;        // 风险级别：HIGH/MEDIUM/LOW
}
```

## 存储结构

### 文件目录结构
```
scan_records/
├── 2024/
│   ├── 12/
│   │   ├── scan_20241204_143022_001.json
│   │   ├── scan_20241204_150315_002.json
│   │   └── ...
│   └── 11/
│       └── ...
└── index.db (SQLite数据库)
```

### 数据库表结构
- **scan_sessions**: 扫描会话索引表
- **scan_statistics**: 快速统计查询表  
- **threat_summary**: 威胁汇总表

## 便捷方法

### 快速查看统计信息
```java
// 获取最近扫描摘要
String summary = ScanStatisticsFactory.getRecentScansSummary(5);
Log.i(TAG, summary);

// 获取今日统计摘要
String todaySummary = ScanStatisticsFactory.getTodayStatisticsSummary();
Log.i(TAG, todaySummary);

// 获取存储使用情况
String storageSummary = ScanStatisticsFactory.getStorageUsageSummary();
Log.i(TAG, storageSummary);
```

### 维护操作
```java
// 清理过期记录
String cleanupResult = ScanStatisticsFactory.cleanupExpiredRecords();
Log.i(TAG, cleanupResult);

// 输出统计功能状态
ScanStatisticsFactory.logStatisticsStatus();
```

## 配置选项

### 默认配置
- **记录保存天数**: 30天
- **单日最大记录数**: 100条
- **性能采样间隔**: 1秒
- **数据库版本**: 1

### 自定义配置
可以通过修改相关常量来调整配置：
- `MAX_RECORD_DAYS`: 最大记录保存天数
- `MAX_RECORDS_PER_DAY`: 单日最大记录数
- `PERFORMANCE_SAMPLE_INTERVAL`: 性能采样间隔

## 注意事项

1. **初始化顺序**: 必须在使用统计功能前调用 `ScanStatisticsFactory.initialize()`
2. **权限要求**: 需要文件读写权限来保存统计数据
3. **存储空间**: 统计数据会占用一定存储空间，建议定期清理
4. **性能影响**: 统计收集对扫描性能影响很小，但会增加少量内存和CPU使用
5. **线程安全**: 所有API都是线程安全的，可以在多线程环境中使用

## 示例代码

完整的使用示例请参考 `StatisticsUsageExample.java` 文件，包含：
- 基本使用方式
- 查询统计数据
- 导出统计报告
- 维护和清理
- 高级用法

## 集成方式

### 方式一：使用集成类（推荐）
```java
// 1. 初始化
AVLEngineStatisticsIntegration.initializeStatistics(context);

// 2. 使用带统计功能的扫描方法
AVLEngineStatisticsIntegration.scanDirWithStatistics("/sdcard/test", originalListener);

// 3. 查看统计结果
String summary = AVLEngineStatisticsIntegration.getRecentScansSummary(5);
Log.i(TAG, summary);
```

### 方式二：直接使用工厂类
```java
// 1. 初始化
ScanStatisticsFactory.initialize(context);

// 2. 创建统计监听器
ScanListener statisticsListener = ScanStatisticsFactory.createStatisticsAwareListener(
    originalListener, "LOCAL", "/sdcard/test");

// 3. 使用统计监听器
AVLEngine.getInstance().scanDir("/sdcard/test", statisticsListener);
```

## 编译和部署

### 编译要求
- Android API Level 21+
- Java 8+（不支持var关键字，已修复为兼容Java 8）
- 需要文件读写权限

### 编译验证
```bash
./gradlew :avl_sdk:compileGreatwallDebugJavaWithJavac
```

编译成功后，所有统计功能类都会被包含在SDK中。

## 故障排除

### 常见问题
1. **统计功能未初始化**: 确保在使用前调用了初始化方法
2. **权限不足**: 检查应用是否有文件读写权限
3. **存储空间不足**: 定期清理过期记录
4. **数据不一致**: 可能是并发访问导致，系统会自动处理
5. **编译错误**: 确保Java版本兼容，已修复var关键字问题

### 调试方法
- 使用 `ScanStatisticsFactory.logStatisticsStatus()` 查看状态
- 检查 AVLEngine.Logger 输出的相关日志
- 验证统计数据的完整性和一致性
- 使用 `AVLEngineStatisticsIntegration.showUsageExample()` 运行示例

## 版本信息

- **版本**: 1.0
- **兼容性**: Android API 21+, Java 8+
- **编译状态**: ✅ 编译通过
- **测试状态**: 待测试
