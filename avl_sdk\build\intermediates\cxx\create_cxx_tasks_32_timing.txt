# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 57ms]
    create-ARM64_V8A-model 14ms
    [gap of 69ms]
  create-initial-cxx-model completed in 140ms
create_cxx_tasks completed in 142ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 64ms
create_cxx_tasks completed in 65ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 71ms
create_cxx_tasks completed in 72ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 76ms
create_cxx_tasks completed in 77ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 76ms
create_cxx_tasks completed in 77ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 58ms]
    create-X86-model 16ms
    [gap of 24ms]
    create-X86-model 14ms
  create-initial-cxx-model completed in 115ms
create_cxx_tasks completed in 118ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 11ms]
    create-ARM64_V8A-model 11ms
    [gap of 103ms]
  create-initial-cxx-model completed in 125ms
create_cxx_tasks completed in 129ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 69ms
create_cxx_tasks completed in 71ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 122ms
create_cxx_tasks completed in 123ms

