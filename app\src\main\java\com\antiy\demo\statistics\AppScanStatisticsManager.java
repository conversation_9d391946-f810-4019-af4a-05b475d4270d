package com.antiy.demo.statistics;

import android.content.Context;
import android.util.Log;

import com.antiy.avlsdk.callback.ScanStatisticsCallback;
import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.entity.ScanErrorType;
import com.antiy.avlsdk.entity.ScanSessionSummary;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 应用层扫描统计管理器
 * 
 * 实现SDK的统计回调接口，负责收集、处理和存储扫描统计数据。
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class AppScanStatisticsManager implements ScanStatisticsCallback {
    
    private static final String TAG = "AppScanStatistics";
    
    private Context context;
    private StatisticsConfig config;
    private ScanSessionCollector currentSession;
    private StatisticsStorage storage;
    
    // 用于管理多个并发会话
    private final ConcurrentMap<String, ScanSessionCollector> activeSessions = new ConcurrentHashMap<>();
    
    public AppScanStatisticsManager(Context context, StatisticsConfig config) {
        this.context = context.getApplicationContext();
        this.config = config;
        
        try {
            this.storage = new StatisticsStorage(config);
            Log.i(TAG, "Statistics manager initialized with storage: " + config.getStorageRootPath());
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize statistics storage", e);
            throw new RuntimeException("Failed to initialize statistics manager", e);
        }
    }
    
    // ==================== ScanStatisticsCallback 实现 ====================
    
    @Override
    public void onScanSessionStart(String sessionId, String scanType, String scanPath, long startTime) {
        Log.d(TAG, "Scan session started: " + sessionId + ", type: " + scanType + ", path: " + scanPath);
        
        try {
            // 检查并发会话数限制
            if (activeSessions.size() >= config.getMaxConcurrentSessions()) {
                Log.w(TAG, "Maximum concurrent sessions reached, cleaning up oldest session");
                cleanupOldestSession();
            }
            
            ScanSessionCollector collector = new ScanSessionCollector(sessionId, scanType, scanPath, startTime, config);
            activeSessions.put(sessionId, collector);
            
            // 如果这是当前主要会话，设置为currentSession
            if (currentSession == null) {
                currentSession = collector;
            }
            
            if (config.isEnableDetailedLogging()) {
                Log.i(TAG, "Session " + sessionId + " started successfully");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error starting scan session: " + sessionId, e);
        }
    }
    
    @Override
    public void onScanProgress(String sessionId, int totalFiles, int scannedFiles) {
        ScanSessionCollector session = activeSessions.get(sessionId);
        if (session != null) {
            session.updateProgress(totalFiles, scannedFiles);
            
            if (config.isEnableDetailedLogging()) {
                Log.d(TAG, "Progress updated for session " + sessionId + ": " + scannedFiles + "/" + totalFiles);
            }
        } else {
            Log.w(TAG, "Progress update for unknown session: " + sessionId);
        }
    }
    
    @Override
    public void onFileScanned(String sessionId, int fileIndex, String filePath, 
                            ResultScan result, long scanTimeMs, String engineType) {
        ScanSessionCollector session = activeSessions.get(sessionId);
        if (session != null) {
            session.recordFileScanned(fileIndex, filePath, result, scanTimeMs, engineType);
            
            if (config.isEnableDetailedLogging()) {
                Log.d(TAG, "File scanned in session " + sessionId + ": " + filePath + 
                     " (" + engineType + ", " + scanTimeMs + "ms)");
            }
        } else {
            Log.w(TAG, "File scan record for unknown session: " + sessionId);
        }
    }
    
    @Override
    public void onScanSessionComplete(String sessionId, ScanSessionSummary summary) {
        Log.d(TAG, "Scan session completed: " + sessionId);
        
        try {
            ScanSessionCollector session = activeSessions.remove(sessionId);
            if (session != null) {
                ScanSessionRecord record = session.buildRecord(summary);
                storage.saveRecord(record);
                
                // 如果这是当前主要会话，清除引用
                if (currentSession == session) {
                    currentSession = null;
                }
                
                Log.i(TAG, "Session " + sessionId + " completed and saved successfully");
            } else {
                Log.w(TAG, "Completion event for unknown session: " + sessionId);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error completing scan session: " + sessionId, e);
        }
    }
    
    @Override
    public void onScanSessionFailed(String sessionId, ScanErrorType errorType, String errorMessage) {
        Log.w(TAG, "Scan session failed: " + sessionId + ", error: " + errorMessage);
        
        try {
            ScanSessionCollector session = activeSessions.remove(sessionId);
            if (session != null) {
                ScanSessionRecord record = session.buildFailedRecord(errorType, errorMessage);
                storage.saveRecord(record);
                
                // 如果这是当前主要会话，清除引用
                if (currentSession == session) {
                    currentSession = null;
                }
                
                Log.i(TAG, "Failed session " + sessionId + " recorded successfully");
            } else {
                Log.w(TAG, "Failure event for unknown session: " + sessionId);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error handling scan session failure: " + sessionId, e);
        }
    }
    
    @Override
    public void onScanSessionCancelled(String sessionId) {
        Log.d(TAG, "Scan session cancelled: " + sessionId);
        
        try {
            ScanSessionCollector session = activeSessions.remove(sessionId);
            if (session != null) {
                ScanSessionRecord record = session.buildCancelledRecord();
                storage.saveRecord(record);
                
                // 如果这是当前主要会话，清除引用
                if (currentSession == session) {
                    currentSession = null;
                }
                
                Log.i(TAG, "Cancelled session " + sessionId + " recorded successfully");
            } else {
                Log.w(TAG, "Cancellation event for unknown session: " + sessionId);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error handling scan session cancellation: " + sessionId, e);
        }
    }
    
    // ==================== 公共查询接口 ====================
    
    /**
     * 获取最近的扫描记录
     * 
     * @param limit 记录数量限制
     * @return 扫描记录列表
     */
    public List<ScanSessionRecord> getRecentScans(int limit) {
        try {
            return storage.getRecentRecords(limit);
        } catch (Exception e) {
            Log.e(TAG, "Error getting recent scans", e);
            return null;
        }
    }
    
    /**
     * 获取指定时间范围的扫描记录
     * 
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 扫描记录列表
     */
    public List<ScanSessionRecord> getScansByDateRange(long startTime, long endTime) {
        try {
            return storage.getRecordsByTimeRange(startTime, endTime);
        } catch (Exception e) {
            Log.e(TAG, "Error getting scans by date range", e);
            return null;
        }
    }
    
    /**
     * 导出统计数据到JSON文件
     * 
     * @param filePath 导出文件路径
     * @param days 导出最近几天的数据
     * @return 导出结果描述
     */
    public String exportToJson(String filePath, int days) {
        try {
            return storage.exportToJson(filePath, days);
        } catch (Exception e) {
            Log.e(TAG, "Error exporting to JSON", e);
            return "Export failed: " + e.getMessage();
        }
    }
    
    /**
     * 导出统计数据到CSV文件
     * 
     * @param filePath 导出文件路径
     * @param days 导出最近几天的数据
     * @return 导出结果描述
     */
    public String exportToCsv(String filePath, int days) {
        try {
            return storage.exportToCsv(filePath, days);
        } catch (Exception e) {
            Log.e(TAG, "Error exporting to CSV", e);
            return "Export failed: " + e.getMessage();
        }
    }
    
    /**
     * 获取统计配置
     * 
     * @return 配置对象
     */
    public StatisticsConfig getConfig() {
        return config;
    }
    
    /**
     * 获取当前活动会话数
     * 
     * @return 活动会话数
     */
    public int getActiveSessionCount() {
        return activeSessions.size();
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 清理最旧的会话
     */
    private void cleanupOldestSession() {
        if (activeSessions.isEmpty()) {
            return;
        }
        
        String oldestSessionId = null;
        long oldestStartTime = Long.MAX_VALUE;
        
        for (ScanSessionCollector session : activeSessions.values()) {
            if (session.getStartTime() < oldestStartTime) {
                oldestStartTime = session.getStartTime();
                oldestSessionId = session.getSessionId();
            }
        }
        
        if (oldestSessionId != null) {
            ScanSessionCollector session = activeSessions.remove(oldestSessionId);
            if (session != null) {
                Log.w(TAG, "Forcibly cleaned up oldest session: " + oldestSessionId);
                // 可以选择保存这个被强制清理的会话
                try {
                    ScanSessionRecord record = session.buildCancelledRecord();
                    storage.saveRecord(record);
                } catch (Exception e) {
                    Log.e(TAG, "Error saving forcibly cleaned session", e);
                }
            }
        }
    }
}
