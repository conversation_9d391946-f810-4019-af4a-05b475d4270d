package com.antiy.avlsdk.statistics.entity;

import java.util.ArrayList;
import java.util.List;

/**
 * 扫描会话记录
 * 记录单次扫描的完整信息，包括统计数据、威胁信息、性能数据等
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ScanSessionRecord {
    
    /** 扫描会话ID，格式：scan_yyyyMMdd_HHmmss_xxx */
    public String scanId;
    
    /** 扫描类型：LOCAL/CLOUD/MIXED */
    public String scanType;
    
    /** 扫描开始时间戳（毫秒） */
    public long startTime;
    
    /** 扫描结束时间戳（毫秒） */
    public long endTime;
    
    /** 扫描耗时（毫秒） */
    public long duration;
    
    /** 扫描状态：RUNNING/COMPLETED/FAILED/CANCELLED */
    public String status;
    
    /** 扫描统计数据 */
    public ScanStatistics statistics;
    
    /** 引擎统计数据 */
    public EngineStatistics engineStats;
    
    /** 威胁信息列表 */
    public List<ThreatInfo> threats;
    
    /** 性能数据 */
    public PerformanceData performance;
    
    /** 错误信息（如果扫描失败） */
    public String errorMessage;
    
    /** 扫描的根目录路径 */
    public String scanPath;
    
    /** 记录创建时间戳 */
    public long createdAt;
    
    public ScanSessionRecord() {
        this.statistics = new ScanStatistics();
        this.engineStats = new EngineStatistics();
        this.threats = new ArrayList<>();
        this.performance = new PerformanceData();
        this.status = "RUNNING";
        this.createdAt = System.currentTimeMillis();
    }
    
    public ScanSessionRecord(String scanId, String scanType, String scanPath) {
        this();
        this.scanId = scanId;
        this.scanType = scanType;
        this.scanPath = scanPath;
        this.startTime = System.currentTimeMillis();
    }
    
    /**
     * 标记扫描完成
     */
    public void markCompleted() {
        this.endTime = System.currentTimeMillis();
        this.duration = this.endTime - this.startTime;
        this.status = "COMPLETED";
    }
    
    /**
     * 标记扫描失败
     */
    public void markFailed(String errorMessage) {
        this.endTime = System.currentTimeMillis();
        this.duration = this.endTime - this.startTime;
        this.status = "FAILED";
        this.errorMessage = errorMessage;
    }
    
    /**
     * 标记扫描取消
     */
    public void markCancelled() {
        this.endTime = System.currentTimeMillis();
        this.duration = this.endTime - this.startTime;
        this.status = "CANCELLED";
    }
    
    /**
     * 添加威胁信息
     */
    public void addThreat(ThreatInfo threat) {
        if (threat != null) {
            this.threats.add(threat);
            this.statistics.maliciousFiles++;
        }
    }
    
    /**
     * 获取威胁数量
     */
    public int getThreatCount() {
        return threats != null ? threats.size() : 0;
    }
    
    /**
     * 获取扫描摘要信息
     */
    public String getSummary() {
        if (statistics == null) {
            return "扫描记录异常";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("扫描").append(getStatusText()).append("：");
        summary.append("共检查").append(statistics.totalFiles).append("个文件");
        
        if (statistics.maliciousFiles > 0) {
            summary.append("，发现").append(statistics.maliciousFiles).append("个威胁");
        } else {
            summary.append("，未发现威胁");
        }
        
        if (duration > 0) {
            summary.append("，耗时").append(formatDuration(duration));
        }
        
        return summary.toString();
    }
    
    /**
     * 获取状态文本
     */
    private String getStatusText() {
        switch (status) {
            case "COMPLETED": return "完成";
            case "FAILED": return "失败";
            case "CANCELLED": return "取消";
            case "RUNNING": return "进行中";
            default: return "未知";
        }
    }
    
    /**
     * 格式化耗时
     */
    private String formatDuration(long durationMs) {
        if (durationMs < 1000) {
            return durationMs + "毫秒";
        } else if (durationMs < 60000) {
            return (durationMs / 1000) + "秒";
        } else {
            long minutes = durationMs / 60000;
            long seconds = (durationMs % 60000) / 1000;
            return minutes + "分" + seconds + "秒";
        }
    }
    
    /**
     * 检查扫描是否完成
     */
    public boolean isCompleted() {
        return "COMPLETED".equals(status) || "FAILED".equals(status) || "CANCELLED".equals(status);
    }
    
    /**
     * 检查扫描是否成功
     */
    public boolean isSuccessful() {
        return "COMPLETED".equals(status);
    }
    
    @Override
    public String toString() {
        return "ScanSessionRecord{" +
                "scanId='" + scanId + '\'' +
                ", scanType='" + scanType + '\'' +
                ", status='" + status + '\'' +
                ", duration=" + duration +
                ", totalFiles=" + (statistics != null ? statistics.totalFiles : 0) +
                ", threats=" + getThreatCount() +
                '}';
    }
}
