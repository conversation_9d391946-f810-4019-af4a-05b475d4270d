package com.antiy.avlsdk.statistics;

import android.content.Context;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.statistics.entity.ScanSessionRecord;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 扫描统计管理器
 * 提供扫描统计数据的主要API接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ScanStatisticsManager {
    
    private static final String TAG = "ScanStatisticsManager";
    
    /** 单例实例 */
    private static volatile ScanStatisticsManager instance;
    
    /** 应用上下文 */
    private Context context;
    
    /** 统计数据收集器 */
    private ScanStatisticsCollector collector;
    
    /** 记录管理器 */
    private ScanRecordManager recordManager;
    
    /** 是否已初始化 */
    private boolean initialized = false;
    
    private ScanStatisticsManager() {
        this.collector = new ScanStatisticsCollector();
    }
    
    /**
     * 获取单例实例
     */
    public static ScanStatisticsManager getInstance() {
        if (instance == null) {
            synchronized (ScanStatisticsManager.class) {
                if (instance == null) {
                    instance = new ScanStatisticsManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化统计管理器
     */
    public boolean initialize(Context context) {
        if (initialized) {
            return true;
        }
        
        try {
            this.context = context.getApplicationContext();
            this.recordManager = ScanRecordManager.getInstance(this.context);
            this.initialized = true;
            
            AVLEngine.Logger.info(TAG + ": Initialized successfully");
            return true;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to initialize: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 开始扫描会话
     */
    public ScanSessionRecord startScanSession(String scanType, String scanPath) {
        if (!initialized) {
            AVLEngine.Logger.warn(TAG + ": Manager not initialized");
            return null;
        }
        
        return collector.startScanSession(scanType, scanPath);
    }
    
    /**
     * 结束扫描会话并保存记录
     */
    public boolean finishScanSession() {
        if (!initialized) {
            return false;
        }
        
        ScanSessionRecord record = collector.finishScanSession();
        if (record != null) {
            return recordManager.saveRecord(record);
        }
        
        return false;
    }
    
    /**
     * 标记扫描会话失败
     */
    public boolean failScanSession(String errorMessage) {
        if (!initialized) {
            return false;
        }
        
        ScanSessionRecord record = collector.failScanSession(errorMessage);
        if (record != null) {
            return recordManager.saveRecord(record);
        }
        
        return false;
    }
    
    /**
     * 取消扫描会话
     */
    public boolean cancelScanSession() {
        if (!initialized) {
            return false;
        }
        
        ScanSessionRecord record = collector.cancelScanSession();
        if (record != null) {
            return recordManager.saveRecord(record);
        }
        
        return false;
    }
    
    /**
     * 获取统计数据收集器
     */
    public ScanStatisticsCollector getCollector() {
        return collector;
    }

    /**
     * 获取记录管理器
     */
    public ScanRecordManager getRecordManager() {
        return recordManager;
    }
    
    /**
     * 获取最近的扫描记录
     */
    public List<ScanSessionRecord> getRecentScans(int limit) {
        if (!initialized) {
            return null;
        }
        
        return recordManager.getRecentRecords(limit);
    }
    
    /**
     * 获取指定日期的扫描记录
     */
    public List<ScanSessionRecord> getScansByDate(String date) {
        if (!initialized) {
            return null;
        }
        
        return recordManager.getRecordsByDate(date);
    }
    
    /**
     * 获取指定时间范围的扫描记录
     */
    public List<ScanSessionRecord> getScansByTimeRange(long startTime, long endTime) {
        if (!initialized) {
            return null;
        }
        
        return recordManager.getRecordsByTimeRange(startTime, endTime);
    }
    
    /**
     * 获取指定扫描记录
     */
    public ScanSessionRecord getScanRecord(String scanId) {
        if (!initialized) {
            return null;
        }
        
        return recordManager.getRecord(scanId);
    }
    
    /**
     * 删除扫描记录
     */
    public boolean deleteScanRecord(String scanId) {
        if (!initialized) {
            return false;
        }
        
        return recordManager.deleteRecord(scanId);
    }
    
    /**
     * 获取日统计数据
     */
    public DailyStatistics getDailyStatistics(String date) {
        if (!initialized) {
            return null;
        }
        
        List<ScanSessionRecord> records = recordManager.getRecordsByDate(date);
        return calculateDailyStatistics(records, date);
    }
    
    /**
     * 获取威胁统计数据
     */
    public List<ThreatStatistics> getThreatStatistics(long startTime, long endTime) {
        if (!initialized) {
            return null;
        }
        
        List<ScanSessionRecord> records = recordManager.getRecordsByTimeRange(startTime, endTime);
        return calculateThreatStatistics(records);
    }
    
    /**
     * 导出统计报告为JSON格式
     */
    public String exportStatisticsReport(long startTime, long endTime, String outputPath) {
        if (!initialized) {
            return null;
        }
        
        try {
            List<ScanSessionRecord> records = recordManager.getRecordsByTimeRange(startTime, endTime);
            JSONObject report = generateStatisticsReport(records, startTime, endTime);
            
            File outputFile = new File(outputPath);
            if (saveJsonToFile(report, outputFile)) {
                AVLEngine.Logger.info(TAG + ": Statistics report exported to: " + outputPath);
                return outputPath;
            }
            
            return null;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to export statistics report: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 导出为CSV格式
     */
    public String exportToCsv(long startTime, long endTime, String outputPath, String encoding) {
        if (!initialized) {
            return null;
        }
        
        try {
            List<ScanSessionRecord> records = recordManager.getRecordsByTimeRange(startTime, endTime);
            
            File outputFile = new File(outputPath);
            if (saveCsvToFile(records, outputFile, encoding)) {
                AVLEngine.Logger.info(TAG + ": CSV report exported to: " + outputPath);
                return outputPath;
            }
            
            return null;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to export CSV report: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 输出统计摘要到日志
     */
    public void logStatisticsSummary(String scanId) {
        if (!initialized) {
            return;
        }
        
        ScanSessionRecord record = recordManager.getRecord(scanId);
        if (record != null) {
            AVLEngine.Logger.info(TAG + ": === 扫描统计摘要 ===");
            AVLEngine.Logger.info(TAG + ": " + record.getSummary());
            AVLEngine.Logger.info(TAG + ": 统计详情: " + record.statistics);
            AVLEngine.Logger.info(TAG + ": 引擎统计: " + record.engineStats);
            AVLEngine.Logger.info(TAG + ": 性能数据: " + record.performance);
            AVLEngine.Logger.info(TAG + ": ==================");
        }
    }
    
    /**
     * 清理过期记录
     */
    public void cleanupExpiredRecords() {
        if (!initialized) {
            return;
        }
        
        recordManager.cleanupExpiredRecords();
    }
    
    /**
     * 获取存储统计信息
     */
    public ScanRecordManager.StorageStats getStorageStats() {
        if (!initialized) {
            return null;
        }
        
        return recordManager.getStorageStats();
    }
    
    /**
     * 计算日统计数据
     */
    private DailyStatistics calculateDailyStatistics(List<ScanSessionRecord> records, String date) {
        DailyStatistics stats = new DailyStatistics();
        stats.date = date;
        
        if (records == null || records.isEmpty()) {
            return stats;
        }
        
        for (ScanSessionRecord record : records) {
            stats.totalScans++;
            
            if (record.statistics != null) {
                stats.totalFiles += record.statistics.totalFiles;
                stats.totalThreats += record.statistics.maliciousFiles;
                stats.totalScanTime += record.duration;
            }
            
            if ("COMPLETED".equals(record.status)) {
                stats.successfulScans++;
            }
        }
        
        if (stats.totalScans > 0) {
            stats.avgScanTime = (double) stats.totalScanTime / stats.totalScans;
        }
        
        return stats;
    }
    
    /**
     * 计算威胁统计数据
     */
    private List<ThreatStatistics> calculateThreatStatistics(List<ScanSessionRecord> records) {
        // 这里需要实现威胁统计的计算逻辑
        // 为了简化，暂时返回空列表
        return new java.util.ArrayList<>();
    }
    
    /**
     * 生成统计报告JSON
     */
    private JSONObject generateStatisticsReport(List<ScanSessionRecord> records, long startTime, long endTime) throws JSONException {
        JSONObject report = new JSONObject();
        
        // 报告基本信息
        report.put("reportType", "scan_statistics");
        report.put("generatedAt", System.currentTimeMillis());
        report.put("timeRange", new JSONObject()
                .put("startTime", startTime)
                .put("endTime", endTime));
        
        // 汇总统计
        JSONObject summary = new JSONObject();
        summary.put("totalScans", records.size());
        
        int totalFiles = 0, totalThreats = 0;
        long totalDuration = 0;
        
        for (ScanSessionRecord record : records) {
            if (record.statistics != null) {
                totalFiles += record.statistics.totalFiles;
                totalThreats += record.statistics.maliciousFiles;
            }
            totalDuration += record.duration;
        }
        
        summary.put("totalFiles", totalFiles);
        summary.put("totalThreats", totalThreats);
        summary.put("totalDuration", totalDuration);
        
        report.put("summary", summary);
        
        // 详细记录
        JSONArray detailsArray = new JSONArray();
        for (ScanSessionRecord record : records) {
            JSONObject recordJson = new JSONObject();
            recordJson.put("scanId", record.scanId);
            recordJson.put("scanType", record.scanType);
            recordJson.put("startTime", record.startTime);
            recordJson.put("duration", record.duration);
            recordJson.put("status", record.status);
            recordJson.put("summary", record.getSummary());
            
            detailsArray.put(recordJson);
        }
        report.put("details", detailsArray);
        
        return report;
    }
    
    /**
     * 保存JSON到文件
     */
    private boolean saveJsonToFile(JSONObject json, File file) {
        BufferedWriter writer = null;
        try {
            writer = new BufferedWriter(new OutputStreamWriter(
                    new FileOutputStream(file), StandardCharsets.UTF_8));
            writer.write(json.toString(2));
            writer.flush();
            return true;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to save JSON file: " + e.getMessage());
            return false;
            
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
        }
    }
    
    /**
     * 保存CSV到文件
     */
    private boolean saveCsvToFile(List<ScanSessionRecord> records, File file, String encoding) {
        BufferedWriter writer = null;
        try {
            writer = new BufferedWriter(new OutputStreamWriter(
                    new FileOutputStream(file), encoding));
            
            // 写入CSV头部
            writer.write("扫描ID,扫描类型,开始时间,结束时间,耗时(秒),总文件数,威胁数,状态,摘要");
            writer.newLine();
            
            // 写入数据行
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            for (ScanSessionRecord record : records) {
                writer.write(String.format("%s,%s,%s,%s,%.1f,%d,%d,%s,\"%s\"",
                        record.scanId,
                        record.scanType,
                        dateFormat.format(new Date(record.startTime)),
                        dateFormat.format(new Date(record.endTime)),
                        record.duration / 1000.0,
                        record.statistics != null ? record.statistics.totalFiles : 0,
                        record.statistics != null ? record.statistics.maliciousFiles : 0,
                        record.status,
                        record.getSummary().replace("\"", "\"\"")));
                writer.newLine();
            }
            
            writer.flush();
            return true;
            
        } catch (Exception e) {
            AVLEngine.Logger.error(TAG + ": Failed to save CSV file: " + e.getMessage());
            return false;
            
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
        }
    }
    
    /**
     * 日统计数据
     */
    public static class DailyStatistics {
        public String date;
        public int totalScans = 0;
        public int successfulScans = 0;
        public int totalFiles = 0;
        public int totalThreats = 0;
        public long totalScanTime = 0;
        public double avgScanTime = 0.0;
        
        @Override
        public String toString() {
            return "DailyStatistics{" +
                    "date='" + date + '\'' +
                    ", scans=" + totalScans +
                    ", files=" + totalFiles +
                    ", threats=" + totalThreats +
                    ", avgTime=" + String.format("%.1f", avgScanTime) + "ms" +
                    '}';
        }
    }
    
    /**
     * 威胁统计数据
     */
    public static class ThreatStatistics {
        public String virusName;
        public int count;
        public long firstDetected;
        public long lastDetected;
        
        @Override
        public String toString() {
            return "ThreatStatistics{" +
                    "virusName='" + virusName + '\'' +
                    ", count=" + count +
                    '}';
        }
    }
}
