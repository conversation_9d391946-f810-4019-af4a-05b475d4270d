package com.antiy.demo.statistics;

import android.util.Log;

import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.entity.ScanErrorType;
import com.antiy.avlsdk.entity.ScanSessionSummary;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 扫描会话数据收集器
 * 
 * 负责在扫描过程中收集和累积统计数据。
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ScanSessionCollector {
    
    private static final String TAG = "ScanSessionCollector";
    
    private final String sessionId;
    private final String scanType;
    private final String scanPath;
    private final long startTime;
    private final StatisticsConfig config;
    
    // 原子计数器，确保线程安全
    private final AtomicInteger totalFiles = new AtomicInteger(0);
    private final AtomicInteger scannedFiles = new AtomicInteger(0);
    private final AtomicInteger threatsFound = new AtomicInteger(0);
    private final AtomicInteger skippedFiles = new AtomicInteger(0);
    private final AtomicLong totalScanTime = new AtomicLong(0);
    
    // 引擎使用统计
    private final ScanSessionRecord.EngineUsageStats engineStats = new ScanSessionRecord.EngineUsageStats();
    
    // 会话记录
    private final ScanSessionRecord record;
    
    public ScanSessionCollector(String sessionId, String scanType, String scanPath, 
                              long startTime, StatisticsConfig config) {
        this.sessionId = sessionId;
        this.scanType = scanType;
        this.scanPath = scanPath;
        this.startTime = startTime;
        this.config = config;
        
        // 初始化记录
        this.record = new ScanSessionRecord(sessionId, scanType, scanPath, startTime);
        
        if (config.isEnableDetailedLogging()) {
            Log.d(TAG, "Session collector created for: " + sessionId);
        }
    }
    
    /**
     * 更新扫描进度
     * 
     * @param totalFileCount 总文件数
     * @param scannedFileCount 已扫描文件数
     */
    public synchronized void updateProgress(int totalFileCount, int scannedFileCount) {
        totalFiles.set(totalFileCount);
        scannedFiles.set(scannedFileCount);
        
        // 计算跳过的文件数
        int currentSkipped = totalFileCount - scannedFileCount;
        if (currentSkipped >= 0) {
            skippedFiles.set(currentSkipped);
        }
        
        if (config.isEnableDetailedLogging()) {
            Log.d(TAG, "Progress updated for " + sessionId + ": " + scannedFileCount + "/" + totalFileCount);
        }
    }
    
    /**
     * 记录文件扫描结果
     * 
     * @param fileIndex 文件索引
     * @param filePath 文件路径
     * @param result 扫描结果
     * @param scanTimeMs 扫描耗时（毫秒）
     * @param engineType 使用的引擎类型
     */
    public synchronized void recordFileScanned(int fileIndex, String filePath, ResultScan result, 
                                             long scanTimeMs, String engineType) {
        try {
            // 累积扫描时间
            totalScanTime.addAndGet(scanTimeMs);
            
            // 记录引擎使用情况
            if (config.isEnableEngineStatistics()) {
                engineStats.recordEngineUsage(engineType, scanTimeMs);
            }
            
            // 处理威胁信息
            if (result != null && result.isMalicious) {
                threatsFound.incrementAndGet();
                
                // 添加威胁详情
                ScanSessionRecord.ThreatInfo threat = new ScanSessionRecord.ThreatInfo(
                    filePath, result.virusName, engineType, scanTimeMs);
                record.addThreat(threat);
                
                if (config.isEnableDetailedLogging()) {
                    Log.i(TAG, "Threat detected in " + sessionId + ": " + filePath + " -> " + result.virusName);
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error recording file scan for session " + sessionId, e);
        }
    }
    
    /**
     * 构建完成的会话记录
     * 
     * @param summary SDK提供的会话摘要
     * @return 完整的会话记录
     */
    public ScanSessionRecord buildRecord(ScanSessionSummary summary) {
        try {
            // 更新基本信息
            record.setEndTime(System.currentTimeMillis());
            record.setStatus("COMPLETED");
            
            // 从摘要中获取数据（如果有的话）
            if (summary != null) {
                record.setTotalFiles(Math.max(summary.totalFiles, totalFiles.get()));
                record.setScannedFiles(Math.max(summary.scannedFiles, scannedFiles.get()));
                record.setThreatsFound(Math.max(summary.threatsFound, threatsFound.get()));
                record.setSkippedFiles(Math.max(summary.skippedFiles, skippedFiles.get()));
                record.setTotalScanTime(Math.max(summary.totalScanTime, totalScanTime.get()));
            } else {
                // 使用收集器的数据
                record.setTotalFiles(totalFiles.get());
                record.setScannedFiles(scannedFiles.get());
                record.setThreatsFound(threatsFound.get());
                record.setSkippedFiles(skippedFiles.get());
                record.setTotalScanTime(totalScanTime.get());
            }
            
            // 设置引擎统计
            record.setEngineStats(engineStats);
            
            if (config.isEnableDetailedLogging()) {
                Log.i(TAG, "Session record built for " + sessionId + ": " + record.toString());
            }
            
            return record;
            
        } catch (Exception e) {
            Log.e(TAG, "Error building session record for " + sessionId, e);
            return createBasicRecord("COMPLETED", null);
        }
    }
    
    /**
     * 构建失败的会话记录
     * 
     * @param errorType 错误类型
     * @param errorMessage 错误消息
     * @return 失败的会话记录
     */
    public ScanSessionRecord buildFailedRecord(ScanErrorType errorType, String errorMessage) {
        try {
            record.setEndTime(System.currentTimeMillis());
            record.setStatus("FAILED");
            record.setErrorMessage(errorMessage);
            
            // 设置当前收集到的数据
            record.setTotalFiles(totalFiles.get());
            record.setScannedFiles(scannedFiles.get());
            record.setThreatsFound(threatsFound.get());
            record.setSkippedFiles(skippedFiles.get());
            record.setTotalScanTime(totalScanTime.get());
            record.setEngineStats(engineStats);
            
            Log.w(TAG, "Failed session record built for " + sessionId + ": " + errorMessage);
            return record;
            
        } catch (Exception e) {
            Log.e(TAG, "Error building failed session record for " + sessionId, e);
            return createBasicRecord("FAILED", errorMessage);
        }
    }
    
    /**
     * 构建取消的会话记录
     * 
     * @return 取消的会话记录
     */
    public ScanSessionRecord buildCancelledRecord() {
        try {
            record.setEndTime(System.currentTimeMillis());
            record.setStatus("CANCELLED");
            
            // 设置当前收集到的数据
            record.setTotalFiles(totalFiles.get());
            record.setScannedFiles(scannedFiles.get());
            record.setThreatsFound(threatsFound.get());
            record.setSkippedFiles(skippedFiles.get());
            record.setTotalScanTime(totalScanTime.get());
            record.setEngineStats(engineStats);
            
            Log.i(TAG, "Cancelled session record built for " + sessionId);
            return record;
            
        } catch (Exception e) {
            Log.e(TAG, "Error building cancelled session record for " + sessionId, e);
            return createBasicRecord("CANCELLED", null);
        }
    }
    
    /**
     * 创建基本的会话记录（用于异常情况）
     * 
     * @param status 状态
     * @param errorMessage 错误消息
     * @return 基本会话记录
     */
    private ScanSessionRecord createBasicRecord(String status, String errorMessage) {
        ScanSessionRecord basicRecord = new ScanSessionRecord(sessionId, scanType, scanPath, startTime);
        basicRecord.setEndTime(System.currentTimeMillis());
        basicRecord.setStatus(status);
        basicRecord.setErrorMessage(errorMessage);
        
        // 设置基本统计数据
        basicRecord.setTotalFiles(totalFiles.get());
        basicRecord.setScannedFiles(scannedFiles.get());
        basicRecord.setThreatsFound(threatsFound.get());
        basicRecord.setSkippedFiles(skippedFiles.get());
        basicRecord.setTotalScanTime(totalScanTime.get());
        
        return basicRecord;
    }
    
    // ==================== Getter方法 ====================
    
    public String getSessionId() {
        return sessionId;
    }
    
    public String getScanType() {
        return scanType;
    }
    
    public String getScanPath() {
        return scanPath;
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public int getTotalFiles() {
        return totalFiles.get();
    }
    
    public int getScannedFiles() {
        return scannedFiles.get();
    }
    
    public int getThreatsFound() {
        return threatsFound.get();
    }
    
    public int getSkippedFiles() {
        return skippedFiles.get();
    }
    
    public long getTotalScanTime() {
        return totalScanTime.get();
    }
    
    public ScanSessionRecord.EngineUsageStats getEngineStats() {
        return engineStats;
    }
    
    @Override
    public String toString() {
        return "ScanSessionCollector{" +
                "sessionId='" + sessionId + '\'' +
                ", scanType='" + scanType + '\'' +
                ", scanPath='" + scanPath + '\'' +
                ", totalFiles=" + totalFiles.get() +
                ", scannedFiles=" + scannedFiles.get() +
                ", threatsFound=" + threatsFound.get() +
                ", skippedFiles=" + skippedFiles.get() +
                ", totalScanTime=" + totalScanTime.get() +
                '}';
    }
}
