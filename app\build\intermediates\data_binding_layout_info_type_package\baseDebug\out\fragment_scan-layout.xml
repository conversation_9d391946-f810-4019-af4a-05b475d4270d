<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_scan" modulePackage="com.antiy.demo" filePath="app\src\main\res\layout\fragment_scan.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_scan_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="677" endOffset="12"/></Target><Target id="@+id/cardQuickScan" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="39" startOffset="12" endLine="91" endOffset="47"/></Target><Target id="@+id/cardFullScan" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="94" startOffset="12" endLine="146" endOffset="47"/></Target><Target id="@+id/cardCustomScan" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="156" startOffset="12" endLine="208" endOffset="47"/></Target><Target id="@+id/cardScheduledScan" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="211" startOffset="12" endLine="264" endOffset="47"/></Target><Target id="@+id/btnSelectFileScan" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="297" startOffset="16" endLine="303" endOffset="52"/></Target><Target id="@+id/textSelectedFile" view="TextView"><Expressions/><location startLine="306" startOffset="16" endLine="314" endOffset="46"/></Target><Target id="@+id/textFileScanResult" view="TextView"><Expressions/><location startLine="317" startOffset="16" endLine="325" endOffset="46"/></Target><Target id="@+id/checkBoxCloudScan" view="CheckBox"><Expressions/><location startLine="382" startOffset="20" endLine="386" endOffset="48"/></Target><Target id="@+id/checkBoxBootScan" view="CheckBox"><Expressions/><location startLine="419" startOffset="20" endLine="423" endOffset="48"/></Target><Target id="@+id/spinnerCpuLimit" view="Spinner"><Expressions/><location startLine="456" startOffset="20" endLine="461" endOffset="75"/></Target><Target id="@+id/buttonStartScan" view="Button"><Expressions/><location startLine="467" startOffset="8" endLine="480" endOffset="38"/></Target></Targets></Layout>