R_DEF: Internal format may change without notice
local
array update_frequency_options
color background_gray
color background_light
color black
color bottom_nav_colors
color danger_red
color divider
color gray
color light_blue
color light_gray
color primary
color primary_variant
color processing_blue
color purple_500
color safe_green
color secondary
color success_green
color warning_yellow
color white
drawable bg_app_logo
drawable bg_circle_blue
drawable bg_edittext_rounded
drawable bg_info_card
drawable button_primary
drawable circle_background_blue
drawable circle_background_light_blue
drawable circle_background_light_red
drawable gray_rounded_button_background
drawable ic_add
drawable ic_antivirus_engine
drawable ic_app
drawable ic_app_manage
drawable ic_arrow_back
drawable ic_arrow_right
drawable ic_back
drawable ic_check
drawable ic_check_circle
drawable ic_cleanup
drawable ic_cloud
drawable ic_custom_scan
drawable ic_dark_mode
drawable ic_deep_scan
drawable ic_delete
drawable ic_email
drawable ic_file
drawable ic_full_scan
drawable ic_help
drawable ic_home
drawable ic_info
drawable ic_language
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_list_empty
drawable ic_logout
drawable ic_more
drawable ic_network
drawable ic_notification
drawable ic_performance
drawable ic_performance_settings
drawable ic_person
drawable ic_play
drawable ic_privacy
drawable ic_protection
drawable ic_quick_scan
drawable ic_realtime_protection
drawable ic_refresh
drawable ic_scan
drawable ic_scan_strategy
drawable ic_schedule
drawable ic_scheduled_scan
drawable ic_settings
drawable ic_shield
drawable ic_shield_logo
drawable ic_support
drawable ic_virus_alert
drawable ic_virus_db
drawable ic_virus_scan
drawable ic_warning
drawable ic_whitelist
drawable rounded_button_background
drawable spinner_background
id appBarLayout
id bottomNavigation
id btnBack
id btnCancel
id btnClearAll
id btnClearCache
id btnDelete
id btnFixAll
id btnIgnore
id btnManageBlackWhiteList
id btnManageCache
id btnPause
id btnRefresh
id btnReport
id btnSave
id btnSaveCloudSizeThreshold
id btnSaveCloudThreshold
id btnScan
id btnSelectFileScan
id btnSetCache
id btnStop
id btnUpdate
id btn_back
id btn_clear_cache
id btn_concurrent_scan
id btn_cpu_continue
id btn_cpu_pause
id btn_hand_update_virus
id btn_scan
id btn_scan_24_hours
id btn_scan_pause
id btn_scan_resume
id btn_scan_stop
id btn_send
id btn_silent_scan
id btn_update_virus
id btn_uuid
id buttonStartScan
id cardAppManage
id cardCleanup
id cardCustomScan
id cardDeepScan
id cardFullScan
id cardMore
id cardNetworkCheck
id cardPerformance
id cardPrivacy
id cardQuickScan
id cardRealTimeProtection
id cardRecentActivities
id cardScanResult
id cardScheduledScan
id cardSecurity
id card_email_support
id card_online_support
id card_scan_help
id card_schedule_help
id card_sdk_status
id card_virus_found_help
id cbAutoUpdate
id cbWifiOnly
id checkBoxBootScan
id checkBoxCloudScan
id chipStatus
id chipType
id clear_cache_ll
id collapsingToolbar
id editCacheCount
id editCloudSizeThreshold
id editCloudThreshold
id emptyView
id etHash
id et_scan_path
id export_statistics_ll
id fabAdd
id ivArrow
id ivNotification
id ivThreatIcon
id iv_app_logo
id layoutAbout
id layoutAntivirusEngine
id layoutButtons
id layoutCacheSize
id layoutCloudScan
id layoutCustomPath
id layoutHelp
id layoutInput
id layoutLanguage
id layoutLogout
id layoutNotification
id layoutPerformance
id layoutScanStrategy
id layoutStats
id layoutUserProfile
id layoutWhitelist
id main
id nav_home
id nav_scan
id nav_settings
id nav_virus_db
id progressBar
id progressCircular
id progressSecurity
id radioBlack
id radioGroupType
id radioWhite
id recyclerView
id rv
id rvScanResults
id rvThreatDetails
id sliderCpuLimit
id spinnerCacheValidity
id spinnerCpuLimit
id spinnerUpdateFrequency
id switchAppProtection
id switchBatterySave
id switchCloud
id switchCpuLimit
id switchDarkMode
id switchFileProtection
id switchMainProtection
id switchNetworkProtection
id switchPrivacyProtection
id switchWifiOnly
id textFileScanResult
id textScanDetails
id textScanStatus
id textScanTime
id textScanType
id textSelectedFile
id textView
id toolbar
id tvCacheSize
id tvCpuLimitValue
id tvCurrentFile
id tvDescription
id tvDetailsTitle
id tvDialogTitle
id tvFileName
id tvFileSize
id tvFullHash
id tvHash
id tvHighRiskCount
id tvLowRiskCount
id tvMediumRiskCount
id tvProgress
id tvProtectionStatus
id tvQuickFunctions
id tvRange
id tvRecentActivities
id tvRiskLevel
id tvSDKStatus
id tvScanStatus
id tvScanSummary
id tvScanType
id tvScannedFiles
id tvScore
id tvSecurityStatus
id tvSecurityTitle
id tvSelectedPath
id tvSignatureCount
id tvStatistics
id tvStatus
id tvStorageSpace
id tvThreatDescription
id tvThreatLocation
id tvThreatName
id tvThreats
id tvTime
id tvTimeRemaining
id tvTitle
id tvType
id tvTypeLabel
id tvUpdateFrequency
id tvVersion
id tvVersionInfo
id tvVirusName
id tv_app_version
id tv_developer
id tv_email
id tv_engine_version
id tv_last_update
id tv_open_source
id tv_privacy_policy
id tv_sdk_status
id tv_service_terms
id tv_support_email
id tv_virus_db_version
id tv_website
id viewPager
id view_statistics_ll
layout activity_about
layout activity_antivirus_settings
layout activity_black_white_list_management
layout activity_cache_management
layout activity_cloud_scan_settings
layout activity_help_feedback
layout activity_home
layout activity_main
layout activity_performance_settings
layout activity_scan_result
layout activity_scanning
layout activity_uuid
layout dialog_add_black_white_item
layout dialog_cache_size_setting
layout fragment_home
layout fragment_protection
layout fragment_scan
layout fragment_settings
layout fragment_virus_database
layout item_black_white_list
layout item_cache_management
layout item_list
layout item_scan_record
layout item_scan_result
layout item_threat_detail
menu bottom_nav_menu
mipmap ic_launcher
mipmap ic_launcher_round
string about
string app_name
style Base.Theme.Demo
style CustomToolbar
style QuickFunctionCard
style QuickFunctionIcon
style QuickFunctionLayout
style QuickFunctionText
style Theme.About
style Theme.AntiVirus
style Theme.Demo
style ToolbarTitleText
style Widget.App.BottomNavigationView
xml backup_rules
xml data_extraction_rules
